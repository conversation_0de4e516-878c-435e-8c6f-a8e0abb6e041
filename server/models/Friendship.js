const mongoose = require('mongoose');

const friendshipSchema = new mongoose.Schema({
  requester: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined', 'blocked'],
    default: 'pending'
  },
  requestedAt: {
    type: Date,
    default: Date.now
  },
  respondedAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Compound index to prevent duplicate friend requests
friendshipSchema.index({ requester: 1, recipient: 1 }, { unique: true });

// Index for performance queries
friendshipSchema.index({ requester: 1, status: 1 });
friendshipSchema.index({ recipient: 1, status: 1 });

// Prevent users from sending friend requests to themselves
friendshipSchema.pre('save', function(next) {
  if (this.requester.equals(this.recipient)) {
    const error = new Error('Users cannot send friend requests to themselves');
    return next(error);
  }
  next();
});

// Static method to get all friends for a user
friendshipSchema.statics.getFriends = async function(userId) {
  const friendships = await this.find({
    $or: [
      { requester: userId, status: 'accepted' },
      { recipient: userId, status: 'accepted' }
    ]
  }).populate('requester recipient', 'username totalPoints weeklyPoints currentLevel');
  
  return friendships.map(friendship => {
    // Return the friend (not the current user)
    return friendship.requester._id.equals(userId) 
      ? friendship.recipient 
      : friendship.requester;
  });
};

// Static method to get pending friend requests for a user
friendshipSchema.statics.getPendingRequests = async function(userId) {
  return this.find({
    recipient: userId,
    status: 'pending'
  }).populate('requester', 'username currentLevel');
};

// Static method to check if friendship exists
friendshipSchema.statics.friendshipExists = async function(userId1, userId2) {
  const friendship = await this.findOne({
    $or: [
      { requester: userId1, recipient: userId2 },
      { requester: userId2, recipient: userId1 }
    ]
  });
  
  return friendship ? friendship.status : null;
};

// Static method to send friend request
friendshipSchema.statics.sendFriendRequest = async function(requesterId, recipientId) {
  // Check if friendship already exists
  const existingStatus = await this.friendshipExists(requesterId, recipientId);
  
  if (existingStatus) {
    throw new Error(`Friendship already exists with status: ${existingStatus}`);
  }
  
  const friendship = new this({
    requester: requesterId,
    recipient: recipientId,
    status: 'pending'
  });
  
  return friendship.save();
};

// Static method to accept friend request
friendshipSchema.statics.acceptFriendRequest = async function(requesterId, recipientId) {
  const friendship = await this.findOneAndUpdate(
    {
      requester: requesterId,
      recipient: recipientId,
      status: 'pending'
    },
    {
      status: 'accepted',
      respondedAt: new Date()
    },
    { new: true }
  );
  
  if (!friendship) {
    throw new Error('Friend request not found or already processed');
  }
  
  return friendship;
};

// Static method to decline friend request
friendshipSchema.statics.declineFriendRequest = async function(requesterId, recipientId) {
  const friendship = await this.findOneAndUpdate(
    {
      requester: requesterId,
      recipient: recipientId,
      status: 'pending'
    },
    {
      status: 'declined',
      respondedAt: new Date()
    },
    { new: true }
  );
  
  if (!friendship) {
    throw new Error('Friend request not found or already processed');
  }
  
  return friendship;
};

// Static method to remove friendship
friendshipSchema.statics.removeFriendship = async function(userId1, userId2) {
  const result = await this.findOneAndDelete({
    $or: [
      { requester: userId1, recipient: userId2 },
      { requester: userId2, recipient: userId1 }
    ]
  });
  
  if (!result) {
    throw new Error('Friendship not found');
  }
  
  return result;
};

module.exports = mongoose.model('Friendship', friendshipSchema);
