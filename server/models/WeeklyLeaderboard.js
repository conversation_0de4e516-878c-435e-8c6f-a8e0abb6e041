const mongoose = require('mongoose');

const weeklyLeaderboardSchema = new mongoose.Schema({
  weekStart: {
    type: Date,
    required: true,
    validate: {
      validator: function(date) {
        // Ensure the date is a Monday (start of week)
        return date.getDay() === 1;
      },
      message: 'Week start must be a Monday'
    }
  },
  weekEnd: {
    type: Date,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  username: {
    type: String,
    required: true
  },
  weeklyPoints: {
    type: Number,
    required: true,
    min: 0
  },
  rank: {
    type: Number,
    required: true,
    min: 1
  },
  previousWeekRank: {
    type: Number,
    min: 1
  },
  rankChange: {
    type: Number,
    default: 0
  },
  sessionsCompleted: {
    type: Number,
    default: 0,
    min: 0
  },
  chunksCompleted: {
    type: Number,
    default: 0,
    min: 0
  },
  averageSessionRating: {
    type: Number,
    min: 1,
    max: 5
  },
  badges: [{
    badgeId: String,
    name: String,
    earnedThisWeek: <PERSON><PERSON><PERSON>
  }],
  achievements: [{
    type: String,
    enum: [
      'top_reader',      // #1 on leaderboard
      'consistent_reader', // Read every day
      'speed_reader',    // Completed sessions quickly
      'diverse_reader',  // Read different content types
      'social_reader',   // Most friend interactions
      'goal_crusher',    // Exceeded weekly goal by 50%
      'comeback_kid',    // Biggest rank improvement
      'steady_climber'   // Consistent rank improvement
    ]
  }]
}, {
  timestamps: true
});

// Compound index for unique user per week
weeklyLeaderboardSchema.index({ weekStart: 1, userId: 1 }, { unique: true });

// Index for leaderboard queries
weeklyLeaderboardSchema.index({ weekStart: 1, rank: 1 });
weeklyLeaderboardSchema.index({ weekStart: 1, weeklyPoints: -1 });
weeklyLeaderboardSchema.index({ userId: 1, weekStart: -1 });

// Virtual for week identifier
weeklyLeaderboardSchema.virtual('weekIdentifier').get(function() {
  const year = this.weekStart.getFullYear();
  const week = Math.ceil((this.weekStart - new Date(year, 0, 1)) / (7 * 24 * 60 * 60 * 1000));
  return `${year}-W${week.toString().padStart(2, '0')}`;
});

// Static method to get current week's leaderboard
weeklyLeaderboardSchema.statics.getCurrentWeekLeaderboard = async function(limit = 50) {
  const currentWeekStart = this.getCurrentWeekStart();
  
  return this.find({ weekStart: currentWeekStart })
    .sort({ rank: 1 })
    .limit(limit)
    .populate('userId', 'profileCustomizations badges currentLevel');
};

// Static method to get user's position in current week
weeklyLeaderboardSchema.statics.getUserCurrentWeekPosition = async function(userId) {
  const currentWeekStart = this.getCurrentWeekStart();
  
  return this.findOne({
    weekStart: currentWeekStart,
    userId: userId
  });
};

// Static method to get user's leaderboard history
weeklyLeaderboardSchema.statics.getUserHistory = async function(userId, weeks = 8) {
  return this.find({ userId })
    .sort({ weekStart: -1 })
    .limit(weeks);
};

// Static method to calculate current week start (Monday)
weeklyLeaderboardSchema.statics.getCurrentWeekStart = function() {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, Monday = 1
  
  const monday = new Date(now);
  monday.setDate(now.getDate() - daysToMonday);
  monday.setHours(0, 0, 0, 0);
  
  return monday;
};

// Static method to update weekly leaderboard
weeklyLeaderboardSchema.statics.updateWeeklyLeaderboard = async function() {
  const User = mongoose.model('User');
  const currentWeekStart = this.getCurrentWeekStart();
  const currentWeekEnd = new Date(currentWeekStart);
  currentWeekEnd.setDate(currentWeekStart.getDate() + 6);
  currentWeekEnd.setHours(23, 59, 59, 999);
  
  // Get all users with their weekly points
  const users = await User.find({ weeklyPoints: { $gt: 0 } })
    .sort({ weeklyPoints: -1, totalPoints: -1 })
    .select('username weeklyPoints');
  
  // Get previous week's ranks for comparison
  const previousWeekStart = new Date(currentWeekStart);
  previousWeekStart.setDate(currentWeekStart.getDate() - 7);
  
  const previousWeekRanks = await this.find({ weekStart: previousWeekStart })
    .select('userId rank');
  
  const previousRankMap = {};
  previousWeekRanks.forEach(entry => {
    previousRankMap[entry.userId.toString()] = entry.rank;
  });
  
  // Update or create leaderboard entries
  const bulkOps = users.map((user, index) => {
    const rank = index + 1;
    const previousRank = previousRankMap[user._id.toString()];
    const rankChange = previousRank ? previousRank - rank : 0;
    
    return {
      updateOne: {
        filter: { weekStart: currentWeekStart, userId: user._id },
        update: {
          $set: {
            weekEnd: currentWeekEnd,
            username: user.username,
            weeklyPoints: user.weeklyPoints,
            rank: rank,
            previousWeekRank: previousRank,
            rankChange: rankChange
          }
        },
        upsert: true
      }
    };
  });
  
  if (bulkOps.length > 0) {
    await this.bulkWrite(bulkOps);
  }
  
  // Award achievements
  await this.awardWeeklyAchievements(currentWeekStart);
  
  return users.length;
};

// Static method to award weekly achievements
weeklyLeaderboardSchema.statics.awardWeeklyAchievements = async function(weekStart) {
  const leaderboard = await this.find({ weekStart }).sort({ rank: 1 });
  
  if (leaderboard.length === 0) return;
  
  // Award top reader achievement
  if (leaderboard[0]) {
    await this.updateOne(
      { _id: leaderboard[0]._id },
      { $addToSet: { achievements: 'top_reader' } }
    );
  }
  
  // Award comeback kid (biggest rank improvement)
  let biggestImprovement = 0;
  let comebackKid = null;
  
  leaderboard.forEach(entry => {
    if (entry.rankChange > biggestImprovement) {
      biggestImprovement = entry.rankChange;
      comebackKid = entry;
    }
  });
  
  if (comebackKid && biggestImprovement >= 5) {
    await this.updateOne(
      { _id: comebackKid._id },
      { $addToSet: { achievements: 'comeback_kid' } }
    );
  }
  
  // Award goal crusher (exceeded weekly goal by 50%)
  const User = mongoose.model('User');
  for (const entry of leaderboard) {
    const user = await User.findById(entry.userId);
    if (user && entry.weeklyPoints >= user.weeklyGoal * 1.5) {
      await this.updateOne(
        { _id: entry._id },
        { $addToSet: { achievements: 'goal_crusher' } }
      );
    }
  }
};

// Static method to reset weekly points for all users
weeklyLeaderboardSchema.statics.resetWeeklyPoints = async function() {
  const User = mongoose.model('User');
  await User.updateMany({}, { weeklyPoints: 0 });
};

// Static method to get leaderboard statistics
weeklyLeaderboardSchema.statics.getLeaderboardStats = async function(weekStart) {
  const stats = await this.aggregate([
    { $match: { weekStart } },
    {
      $group: {
        _id: null,
        totalParticipants: { $sum: 1 },
        averagePoints: { $avg: '$weeklyPoints' },
        totalPoints: { $sum: '$weeklyPoints' },
        topScore: { $max: '$weeklyPoints' },
        averageSessionsCompleted: { $avg: '$sessionsCompleted' }
      }
    }
  ]);
  
  return stats[0] || {
    totalParticipants: 0,
    averagePoints: 0,
    totalPoints: 0,
    topScore: 0,
    averageSessionsCompleted: 0
  };
};

module.exports = mongoose.model('WeeklyLeaderboard', weeklyLeaderboardSchema);
