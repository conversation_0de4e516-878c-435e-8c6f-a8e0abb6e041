const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const validator = require('validator');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [20, 'Username cannot exceed 20 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long']
  },
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required'],
    validate: {
      validator: function(date) {
        const age = Math.floor((Date.now() - date.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        return age >= 6 && age <= 16;
      },
      message: 'Age must be between 6 and 16 years old'
    }
  },
  totalPoints: {
    type: Number,
    default: 0,
    min: 0
  },
  weeklyPoints: {
    type: Number,
    default: 0,
    min: 0
  },
  currentLevel: {
    type: Number,
    default: 1,
    min: 1
  },
  chunkSizeLevel: {
    type: Number,
    default: 1,
    min: 1,
    max: 10
  },
  interests: [{
    type: String,
    enum: ['science', 'history', 'sports', 'animals', 'technology', 'art', 'music', 'nature', 'space', 'adventure']
  }],
  profileCustomizations: {
    avatar: {
      type: String,
      default: 'default'
    },
    theme: {
      type: String,
      default: 'default',
      enum: ['default', 'ocean', 'forest', 'space', 'rainbow']
    },
    badgeFrame: {
      type: String,
      default: 'default'
    }
  },
  badges: [{
    badgeId: String,
    name: String,
    earnedAt: {
      type: Date,
      default: Date.now
    }
  }],
  friends: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  lastContentRefresh: {
    type: Date,
    default: Date.now
  },
  weeklyGoal: {
    type: Number,
    default: 100
  }
}, {
  timestamps: true
});

// Index for performance
userSchema.index({ username: 1 });
userSchema.index({ weeklyPoints: -1 });
userSchema.index({ totalPoints: -1 });

// Virtual for age calculation
userSchema.virtual('age').get(function() {
  return Math.floor((Date.now() - this.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to calculate chunk size based on level and age
userSchema.methods.getChunkSize = function(contentType) {
  const baseSizes = {
    article: { min: 50, max: 200 },
    video: { min: 2, max: 8 },
    book: { min: 100, max: 500 }
  };
  
  const base = baseSizes[contentType] || baseSizes.article;
  const ageMultiplier = Math.min(this.age / 10, 1.5);
  const levelMultiplier = Math.min(this.chunkSizeLevel * 0.3, 2);
  
  return {
    min: Math.floor(base.min * ageMultiplier * levelMultiplier),
    max: Math.floor(base.max * ageMultiplier * levelMultiplier)
  };
};

// Method to add points and check for level ups
userSchema.methods.addPoints = function(points) {
  this.totalPoints += points;
  this.weeklyPoints += points;
  
  // Level up logic (every 500 points)
  const newLevel = Math.floor(this.totalPoints / 500) + 1;
  if (newLevel > this.currentLevel) {
    this.currentLevel = newLevel;
    // Increase chunk size level every 3 levels
    if (newLevel % 3 === 0) {
      this.chunkSizeLevel = Math.min(this.chunkSizeLevel + 1, 10);
    }
    return { levelUp: true, newLevel };
  }
  
  return { levelUp: false };
};

// Method to reset weekly points (called by cron job)
userSchema.methods.resetWeeklyPoints = function() {
  this.weeklyPoints = 0;
};

module.exports = mongoose.model('User', userSchema);
