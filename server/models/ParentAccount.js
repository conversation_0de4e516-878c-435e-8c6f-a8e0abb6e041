const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const parentAccountSchema = new mongoose.Schema({
  childUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  parentPassword: {
    type: String,
    required: [true, 'Parent password is required'],
    minlength: [8, 'Parent password must be at least 8 characters long']
  },
  parentEmail: {
    type: String,
    required: [true, 'Parent email is required'],
    unique: true,
    lowercase: true,
    validate: {
      validator: function(email) {
        return /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email);
      },
      message: 'Please enter a valid email address'
    }
  },
  weeklyGoalPoints: {
    type: Number,
    default: 100,
    min: 0,
    max: 1000
  },
  monthlyGoalPoints: {
    type: Number,
    default: 400,
    min: 0,
    max: 5000
  },
  contentRestrictions: {
    allowedContentTypes: [{
      type: String,
      enum: ['article', 'video', 'book'],
      default: ['article', 'video', 'book']
    }],
    blockedTopics: [{
      type: String
    }],
    maxDailyScreenTime: {
      type: Number, // in minutes
      default: 120,
      min: 30,
      max: 300
    },
    allowFriends: {
      type: Boolean,
      default: true
    }
  },
  notifications: {
    weeklyProgress: {
      type: Boolean,
      default: true
    },
    goalAchievements: {
      type: Boolean,
      default: true
    },
    friendRequests: {
      type: Boolean,
      default: true
    },
    contentRecommendations: {
      type: Boolean,
      default: false
    }
  },
  lastLogin: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for performance
parentAccountSchema.index({ childUserId: 1 });
parentAccountSchema.index({ parentEmail: 1 });

// Hash parent password before saving
parentAccountSchema.pre('save', async function(next) {
  if (!this.isModified('parentPassword')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.parentPassword = await bcrypt.hash(this.parentPassword, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare parent passwords
parentAccountSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.parentPassword);
};

// Method to check if content type is allowed
parentAccountSchema.methods.isContentTypeAllowed = function(contentType) {
  return this.contentRestrictions.allowedContentTypes.includes(contentType);
};

// Method to check if topic is blocked
parentAccountSchema.methods.isTopicBlocked = function(topic) {
  return this.contentRestrictions.blockedTopics.some(blocked => 
    topic.toLowerCase().includes(blocked.toLowerCase())
  );
};

// Method to update goals
parentAccountSchema.methods.updateGoals = function(weeklyGoal, monthlyGoal) {
  if (weeklyGoal !== undefined) {
    this.weeklyGoalPoints = Math.max(0, Math.min(1000, weeklyGoal));
  }
  if (monthlyGoal !== undefined) {
    this.monthlyGoalPoints = Math.max(0, Math.min(5000, monthlyGoal));
  }
};

module.exports = mongoose.model('ParentAccount', parentAccountSchema);
