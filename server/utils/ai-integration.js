const OpenAI = require('openai');

class ContentCurator {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.lastContentRefresh = {};
    this.contentCache = {};
    this.refreshIntervalHours = parseInt(process.env.CONTENT_REFRESH_INTERVAL_HOURS) || 24;
  }

  // Main method to get curated content
  async getCuratedContent(userId, userAge, interests = [], forceRefresh = false) {
    try {
      // Check if content needs refresh
      if (!forceRefresh && !this.shouldRefreshContent(userId)) {
        const cachedContent = this.getCachedContent(userId);
        if (cachedContent) {
          return cachedContent;
        }
      }

      // Generate new content recommendations
      const content = await this.generateContentRecommendations(userAge, interests);
      
      // Cache the content
      this.cacheContent(userId, content);
      this.lastContentRefresh[userId] = new Date();

      return content;
    } catch (error) {
      console.error('Content curation error:', error);
      
      // Return cached content if available, otherwise return fallback
      const cachedContent = this.getCachedContent(userId);
      if (cachedContent) {
        return cachedContent;
      }
      
      return this.getFallbackContent(userAge);
    }
  }

  // Check if content should be refreshed
  shouldRefreshContent(userId) {
    const lastRefresh = this.lastContentRefresh[userId];
    if (!lastRefresh) return true;
    
    const hoursSinceRefresh = (Date.now() - lastRefresh.getTime()) / (1000 * 60 * 60);
    return hoursSinceRefresh >= this.refreshIntervalHours;
  }

  // Generate content recommendations using OpenAI
  async generateContentRecommendations(userAge, interests) {
    const prompt = this.buildContentPrompt(userAge, interests);
    
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{
        role: "system",
        content: `You are an expert content curator for children's educational media. 
        You specialize in finding age-appropriate, engaging, and educational content from reputable sources.
        Always prioritize safety, educational value, and age-appropriateness.
        Provide content that is both fun and educational.`
      }, {
        role: "user",
        content: prompt
      }],
      max_tokens: 2000,
      temperature: 0.7
    });

    const contentText = response.choices[0].message.content;
    return this.parseContentRecommendations(contentText, userAge);
  }

  // Build the prompt for OpenAI
  buildContentPrompt(age, interests) {
    const interestsList = interests.length > 0 ? interests.join(', ') : 'general learning, science, nature, adventure';
    
    return `Recommend 12 educational content pieces for a ${age}-year-old child.
    
    Include exactly:
    - 4 news articles from kid-friendly sources (Newsela, Time for Kids, National Geographic Kids, Scholastic News)
    - 4 educational YouTube videos from trusted channels (Crash Course Kids, National Geographic Kids, SciShow Kids, TED-Ed)
    - 4 online books/stories from free sources (Project Gutenberg, Open Library, or educational websites)
    
    Child's interests: ${interestsList}
    
    For each item, provide:
    - title: Clear, engaging title
    - url: Valid, working URL (verify these are real sources)
    - type: "article", "video", or "book"
    - source: The website/channel name
    - estimated_duration: Reading/viewing time in minutes
    - difficulty_level: 1-10 scale (${Math.max(1, age - 3)} to ${Math.min(10, age + 2)} for this age)
    - description: Brief, exciting description (2-3 sentences)
    - topics: Array of 2-3 relevant topics/subjects
    
    Format as valid JSON array. Ensure all URLs are from reputable, child-safe sources.
    Prioritize content that is both educational and engaging for a ${age}-year-old.`;
  }

  // Parse OpenAI response into structured content
  parseContentRecommendations(contentText, userAge) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = contentText.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON array found in response');
      }

      const contentArray = JSON.parse(jsonMatch[0]);
      
      // Validate and clean the content
      const validatedContent = contentArray
        .filter(item => this.validateContentItem(item))
        .map(item => this.sanitizeContentItem(item, userAge))
        .slice(0, 12); // Limit to 12 items

      // Ensure we have content for each type
      const contentByType = {
        article: validatedContent.filter(item => item.type === 'article').slice(0, 4),
        video: validatedContent.filter(item => item.type === 'video').slice(0, 4),
        book: validatedContent.filter(item => item.type === 'book').slice(0, 4)
      };

      // Fill in with fallback content if needed
      Object.keys(contentByType).forEach(type => {
        while (contentByType[type].length < 4) {
          contentByType[type].push(this.getFallbackContentItem(type, userAge));
        }
      });

      return {
        articles: contentByType.article,
        videos: contentByType.video,
        books: contentByType.book,
        generatedAt: new Date(),
        userAge
      };
    } catch (error) {
      console.error('Error parsing content recommendations:', error);
      return this.getFallbackContent(userAge);
    }
  }

  // Validate a content item
  validateContentItem(item) {
    return (
      item &&
      typeof item.title === 'string' &&
      typeof item.url === 'string' &&
      ['article', 'video', 'book'].includes(item.type) &&
      typeof item.source === 'string' &&
      typeof item.estimated_duration === 'number' &&
      typeof item.difficulty_level === 'number' &&
      typeof item.description === 'string' &&
      Array.isArray(item.topics)
    );
  }

  // Sanitize and enhance a content item
  sanitizeContentItem(item, userAge) {
    return {
      id: this.generateContentId(),
      title: item.title.substring(0, 200),
      url: item.url,
      type: item.type,
      source: item.source.substring(0, 100),
      estimatedDuration: Math.max(1, Math.min(60, item.estimated_duration)),
      difficultyLevel: Math.max(1, Math.min(10, item.difficulty_level)),
      description: item.description.substring(0, 500),
      topics: item.topics.slice(0, 5).map(topic => topic.substring(0, 50)),
      ageAppropriate: this.isAgeAppropriate(item, userAge),
      createdAt: new Date()
    };
  }

  // Check if content is age appropriate
  isAgeAppropriate(item, userAge) {
    const minAge = Math.max(6, userAge - 2);
    const maxAge = Math.min(16, userAge + 3);
    return item.difficulty_level >= minAge && item.difficulty_level <= maxAge;
  }

  // Generate unique content ID
  generateContentId() {
    return 'content_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Cache content for a user
  cacheContent(userId, content) {
    this.contentCache[userId] = {
      content,
      cachedAt: new Date()
    };
  }

  // Get cached content for a user
  getCachedContent(userId) {
    const cached = this.contentCache[userId];
    if (!cached) return null;
    
    const hoursSinceCached = (Date.now() - cached.cachedAt.getTime()) / (1000 * 60 * 60);
    if (hoursSinceCached >= this.refreshIntervalHours) {
      delete this.contentCache[userId];
      return null;
    }
    
    return cached.content;
  }

  // Get fallback content when AI fails
  getFallbackContent(userAge) {
    return {
      articles: [
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge),
        this.getFallbackContentItem('article', userAge)
      ],
      videos: [
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge),
        this.getFallbackContentItem('video', userAge)
      ],
      books: [
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge),
        this.getFallbackContentItem('book', userAge)
      ],
      generatedAt: new Date(),
      userAge,
      isFallback: true
    };
  }

  // Get a single fallback content item
  getFallbackContentItem(type, userAge) {
    const fallbackContent = {
      article: {
        title: "Explore the Amazing World of Science",
        url: "https://www.natgeokids.com/uk/discover/science/",
        source: "National Geographic Kids",
        estimatedDuration: 5,
        description: "Discover fascinating facts about our world through fun science articles and experiments.",
        topics: ["science", "discovery", "nature"]
      },
      video: {
        title: "How Things Work - Educational Videos",
        url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", // Placeholder
        source: "Educational Channel",
        estimatedDuration: 8,
        description: "Learn how everyday things work through fun and engaging videos.",
        topics: ["science", "technology", "learning"]
      },
      book: {
        title: "Classic Children's Stories",
        url: "https://www.gutenberg.org/browse/categories/1",
        source: "Project Gutenberg",
        estimatedDuration: 15,
        description: "Enjoy timeless stories that have entertained children for generations.",
        topics: ["stories", "adventure", "imagination"]
      }
    };

    const baseItem = fallbackContent[type];
    return {
      id: this.generateContentId(),
      ...baseItem,
      type,
      difficultyLevel: Math.max(3, Math.min(8, userAge)),
      ageAppropriate: true,
      createdAt: new Date(),
      isFallback: true
    };
  }

  // Clear cache for a user
  clearUserCache(userId) {
    delete this.contentCache[userId];
    delete this.lastContentRefresh[userId];
  }

  // Clear all cache
  clearAllCache() {
    this.contentCache = {};
    this.lastContentRefresh = {};
  }
}

module.exports = ContentCurator;
