const express = require('express');
const { User, Friendship, WeeklyLeaderboard } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get weekly leaderboard
router.get('/weekly-leaderboard', authenticateToken, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    
    const leaderboard = await WeeklyLeaderboard.getCurrentWeekLeaderboard(parseInt(limit));
    
    // Add current user's position if not in top results
    const userPosition = await WeeklyLeaderboard.getUserCurrentWeekPosition(req.userId);
    
    const response = {
      success: true,
      leaderboard: leaderboard.map(entry => ({
        rank: entry.rank,
        username: entry.username,
        weeklyPoints: entry.weeklyPoints,
        rankChange: entry.rankChange,
        achievements: entry.achievements,
        isCurrentUser: entry.userId.toString() === req.userId.toString()
      })),
      userPosition: userPosition ? {
        rank: userPosition.rank,
        weeklyPoints: userPosition.weeklyPoints,
        rankChange: userPosition.rankChange
      } : null
    };
    
    res.json(response);
    
  } catch (error) {
    console.error('Get weekly leaderboard error:', error);
    res.status(500).json({
      error: 'Failed to get weekly leaderboard',
      code: 'GET_LEADERBOARD_ERROR'
    });
  }
});

// Get user's friends list
router.get('/friends', authenticateToken, async (req, res) => {
  try {
    const friends = await Friendship.getFriends(req.userId);
    
    const friendsWithStats = friends.map(friend => ({
      id: friend._id,
      username: friend.username,
      currentLevel: friend.currentLevel,
      totalPoints: friend.totalPoints,
      weeklyPoints: friend.weeklyPoints
    }));
    
    res.json({
      success: true,
      friends: friendsWithStats,
      friendCount: friendsWithStats.length
    });
    
  } catch (error) {
    console.error('Get friends error:', error);
    res.status(500).json({
      error: 'Failed to get friends list',
      code: 'GET_FRIENDS_ERROR'
    });
  }
});

// Get pending friend requests
router.get('/friend-requests', authenticateToken, async (req, res) => {
  try {
    const pendingRequests = await Friendship.getPendingRequests(req.userId);
    
    const requests = pendingRequests.map(request => ({
      id: request._id,
      requester: {
        id: request.requester._id,
        username: request.requester.username,
        currentLevel: request.requester.currentLevel
      },
      requestedAt: request.requestedAt
    }));
    
    res.json({
      success: true,
      requests,
      requestCount: requests.length
    });
    
  } catch (error) {
    console.error('Get friend requests error:', error);
    res.status(500).json({
      error: 'Failed to get friend requests',
      code: 'GET_REQUESTS_ERROR'
    });
  }
});

// Send friend request
router.post('/friend-request', authenticateToken, async (req, res) => {
  try {
    const { targetUsername } = req.body;
    
    if (!targetUsername) {
      return res.status(400).json({
        error: 'Target username is required',
        code: 'MISSING_USERNAME'
      });
    }
    
    // Find target user
    const targetUser = await User.findOne({ username: targetUsername.toLowerCase() });
    if (!targetUser) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // Check if user is trying to add themselves
    if (targetUser._id.toString() === req.userId.toString()) {
      return res.status(400).json({
        error: 'Cannot send friend request to yourself',
        code: 'SELF_REQUEST'
      });
    }
    
    // Send friend request
    const friendship = await Friendship.sendFriendRequest(req.userId, targetUser._id);
    
    res.status(201).json({
      success: true,
      message: `Friend request sent to ${targetUser.username}`,
      friendship: {
        id: friendship._id,
        status: friendship.status,
        requestedAt: friendship.requestedAt
      }
    });
    
  } catch (error) {
    console.error('Send friend request error:', error);
    
    if (error.message.includes('already exists')) {
      return res.status(409).json({
        error: error.message,
        code: 'FRIENDSHIP_EXISTS'
      });
    }
    
    res.status(500).json({
      error: 'Failed to send friend request',
      code: 'SEND_REQUEST_ERROR'
    });
  }
});

// Accept friend request
router.post('/accept-friend/:requesterId', authenticateToken, async (req, res) => {
  try {
    const { requesterId } = req.params;
    
    // Validate requester ID
    if (!requesterId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        error: 'Invalid requester ID',
        code: 'INVALID_ID'
      });
    }
    
    const friendship = await Friendship.acceptFriendRequest(requesterId, req.userId);
    
    // Get requester info for response
    const requester = await User.findById(requesterId).select('username');
    
    res.json({
      success: true,
      message: `You are now friends with ${requester.username}!`,
      friendship: {
        id: friendship._id,
        status: friendship.status,
        respondedAt: friendship.respondedAt
      }
    });
    
  } catch (error) {
    console.error('Accept friend request error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Friend request not found',
        code: 'REQUEST_NOT_FOUND'
      });
    }
    
    res.status(500).json({
      error: 'Failed to accept friend request',
      code: 'ACCEPT_REQUEST_ERROR'
    });
  }
});

// Decline friend request
router.post('/decline-friend/:requesterId', authenticateToken, async (req, res) => {
  try {
    const { requesterId } = req.params;
    
    // Validate requester ID
    if (!requesterId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        error: 'Invalid requester ID',
        code: 'INVALID_ID'
      });
    }
    
    const friendship = await Friendship.declineFriendRequest(requesterId, req.userId);
    
    res.json({
      success: true,
      message: 'Friend request declined',
      friendship: {
        id: friendship._id,
        status: friendship.status,
        respondedAt: friendship.respondedAt
      }
    });
    
  } catch (error) {
    console.error('Decline friend request error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Friend request not found',
        code: 'REQUEST_NOT_FOUND'
      });
    }
    
    res.status(500).json({
      error: 'Failed to decline friend request',
      code: 'DECLINE_REQUEST_ERROR'
    });
  }
});

// Remove friend
router.delete('/friend/:friendId', authenticateToken, async (req, res) => {
  try {
    const { friendId } = req.params;
    
    // Validate friend ID
    if (!friendId.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        error: 'Invalid friend ID',
        code: 'INVALID_ID'
      });
    }
    
    const friendship = await Friendship.removeFriendship(req.userId, friendId);
    
    res.json({
      success: true,
      message: 'Friend removed successfully'
    });
    
  } catch (error) {
    console.error('Remove friend error:', error);
    
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Friendship not found',
        code: 'FRIENDSHIP_NOT_FOUND'
      });
    }
    
    res.status(500).json({
      error: 'Failed to remove friend',
      code: 'REMOVE_FRIEND_ERROR'
    });
  }
});

// Get friends' recent activity
router.get('/friends-activity', authenticateToken, async (req, res) => {
  try {
    const friends = await Friendship.getFriends(req.userId);
    const friendIds = friends.map(friend => friend._id);
    
    if (friendIds.length === 0) {
      return res.json({
        success: true,
        activities: [],
        message: 'Add friends to see their reading activity!'
      });
    }
    
    // Get recent completed sessions from friends
    const ContentSession = require('../models/ContentSession');
    const recentActivities = await ContentSession.find({
      userId: { $in: friendIds },
      status: 'completed',
      completedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
    })
    .populate('userId', 'username currentLevel')
    .sort({ completedAt: -1 })
    .limit(20);
    
    const activities = recentActivities.map(session => ({
      id: session._id,
      user: {
        username: session.userId.username,
        level: session.userId.currentLevel
      },
      contentTitle: session.contentTitle,
      contentType: session.contentType,
      pointsEarned: session.pointsEarned,
      completedAt: session.completedAt,
      timeAgo: getTimeAgo(session.completedAt)
    }));
    
    res.json({
      success: true,
      activities
    });
    
  } catch (error) {
    console.error('Get friends activity error:', error);
    res.status(500).json({
      error: 'Failed to get friends activity',
      code: 'GET_ACTIVITY_ERROR'
    });
  }
});

// Search users for friend requests
router.get('/search-users', authenticateToken, async (req, res) => {
  try {
    const { query, limit = 10 } = req.query;
    
    if (!query || query.length < 2) {
      return res.status(400).json({
        error: 'Search query must be at least 2 characters',
        code: 'INVALID_QUERY'
      });
    }
    
    // Search for users by username (case insensitive)
    const users = await User.find({
      username: { $regex: query, $options: 'i' },
      _id: { $ne: req.userId } // Exclude current user
    })
    .select('username currentLevel totalPoints')
    .limit(parseInt(limit));
    
    // Check friendship status for each user
    const usersWithStatus = await Promise.all(
      users.map(async (user) => {
        const friendshipStatus = await Friendship.friendshipExists(req.userId, user._id);
        return {
          id: user._id,
          username: user.username,
          currentLevel: user.currentLevel,
          totalPoints: user.totalPoints,
          friendshipStatus: friendshipStatus || 'none'
        };
      })
    );
    
    res.json({
      success: true,
      users: usersWithStatus
    });
    
  } catch (error) {
    console.error('Search users error:', error);
    res.status(500).json({
      error: 'Failed to search users',
      code: 'SEARCH_USERS_ERROR'
    });
  }
});

// Helper function to get time ago string
function getTimeAgo(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
}

module.exports = router;
