const express = require('express');
const { User, ContentSession } = require('../models');
const { authenticateToken, checkContentRestrictions, validateContentAccess, checkScreenTimeLimit } = require('../middleware/auth');
const ContentCurator = require('../utils/ai-integration');
const ContentChunker = require('../utils/content-chunker');

const router = express.Router();
const contentCurator = new ContentCurator();
const contentChunker = new ContentChunker();

// Get curated content for user
router.get('/curate', authenticateToken, checkContentRestrictions, async (req, res) => {
  try {
    const { forceRefresh = false, contentType } = req.query;
    const user = req.user;
    const age = Math.floor((Date.now() - user.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000));

    // Get curated content from AI
    const curatedContent = await contentCurator.getCuratedContent(
      user._id,
      age,
      user.interests,
      forceRefresh === 'true'
    );

    // Filter content based on parent restrictions if they exist
    let filteredContent = curatedContent;
    if (req.contentRestrictions) {
      filteredContent = this.filterContentByRestrictions(curatedContent, req.contentRestrictions);
    }

    // If specific content type requested, return only that type
    if (contentType && ['article', 'video', 'book'].includes(contentType)) {
      const typeKey = contentType + 's';
      filteredContent = {
        [typeKey]: filteredContent[typeKey] || [],
        generatedAt: filteredContent.generatedAt,
        userAge: filteredContent.userAge
      };
    }

    res.json({
      success: true,
      content: filteredContent,
      userLevel: user.currentLevel,
      chunkSizeLevel: user.chunkSizeLevel,
      hasParentRestrictions: !!req.contentRestrictions
    });

  } catch (error) {
    console.error('Content curation error:', error);
    res.status(500).json({
      error: 'Failed to curate content',
      code: 'CURATION_ERROR'
    });
  }
});

// Start a new content session
router.post('/start-session', authenticateToken, checkContentRestrictions, validateContentAccess, checkScreenTimeLimit, async (req, res) => {
  try {
    const { contentUrl, contentTitle, contentType, contentSource, estimatedDuration, difficultyLevel, content } = req.body;

    // Validation
    if (!contentUrl || !contentTitle || !contentType) {
      return res.status(400).json({
        error: 'Content URL, title, and type are required',
        code: 'MISSING_CONTENT_DATA'
      });
    }

    if (!['article', 'video', 'book'].includes(contentType)) {
      return res.status(400).json({
        error: 'Invalid content type',
        code: 'INVALID_CONTENT_TYPE'
      });
    }

    // Check if user already has an active session for this content
    const existingSession = await ContentSession.findOne({
      userId: req.userId,
      contentUrl,
      status: { $in: ['active', 'paused'] }
    });

    if (existingSession) {
      return res.json({
        success: true,
        session: existingSession,
        message: 'Resuming existing session'
      });
    }

    // Chunk the content based on user's chunk size level
    const user = req.user;
    let chunkedContent;

    try {
      chunkedContent = await contentChunker.chunkContent(
        {
          title: contentTitle,
          url: contentUrl,
          content: content || `Start ${contentType === 'video' ? 'watching' : 'reading'}: ${contentTitle}`
        },
        user.chunkSizeLevel,
        contentType
      );
    } catch (chunkingError) {
      console.error('Content chunking error:', chunkingError);
      // Create a simple single-chunk fallback
      chunkedContent = {
        title: contentTitle,
        url: contentUrl,
        type: contentType,
        totalChunks: 1,
        chunks: [{
          index: 0,
          content: `Start ${contentType === 'video' ? 'watching' : 'reading'}: ${contentTitle}`,
          type: contentType,
          metadata: { isFallback: true },
          id: `chunk_0_${Date.now()}`
        }]
      };
    }

    // Create new content session
    const session = new ContentSession({
      userId: req.userId,
      contentUrl,
      contentTitle,
      contentType,
      contentSource: contentSource || 'Unknown',
      estimatedDuration: estimatedDuration || 10,
      difficultyLevel: difficultyLevel || Math.max(3, Math.min(8, user.age || 10)),
      totalChunks: chunkedContent.totalChunks,
      completedChunks: 0,
      chunkDetails: [],
      pointsEarned: 0,
      status: 'active'
    });

    await session.save();

    // Update user's last content refresh if needed
    if (Date.now() - user.lastContentRefresh.getTime() > 24 * 60 * 60 * 1000) {
      user.lastContentRefresh = new Date();
      await user.save();
    }

    res.status(201).json({
      success: true,
      session: {
        id: session._id,
        contentTitle: session.contentTitle,
        contentType: session.contentType,
        contentUrl: session.contentUrl,
        totalChunks: session.totalChunks,
        completedChunks: session.completedChunks,
        status: session.status,
        startedAt: session.startedAt
      },
      chunkedContent,
      remainingScreenTime: req.remainingScreenTime
    });

  } catch (error) {
    console.error('Start session error:', error);
    res.status(500).json({
      error: 'Failed to start content session',
      code: 'SESSION_START_ERROR'
    });
  }
});

// Complete a chunk in a session
router.post('/complete-chunk/:sessionId', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { chunkIndex, timeSpent = 0, userFeedback } = req.body;

    // Find the session
    const session = await ContentSession.findOne({
      _id: sessionId,
      userId: req.userId,
      status: { $in: ['active', 'paused'] }
    });

    if (!session) {
      return res.status(404).json({
        error: 'Session not found or not active',
        code: 'SESSION_NOT_FOUND'
      });
    }

    // Validate chunk index
    if (chunkIndex < 0 || chunkIndex >= session.totalChunks) {
      return res.status(400).json({
        error: 'Invalid chunk index',
        code: 'INVALID_CHUNK_INDEX'
      });
    }

    // Complete the chunk
    const chunkResult = session.completeChunk(chunkIndex, timeSpent);
    
    // Add user feedback if provided
    if (userFeedback && typeof userFeedback === 'string') {
      session.userFeedback = userFeedback.substring(0, 500);
    }

    await session.save();

    // Update user points
    const user = req.user;
    const levelUpResult = user.addPoints(chunkResult.chunkPoints);
    await user.save();

    // Check for badges or achievements
    const badges = await this.checkForNewBadges(user, session);

    res.json({
      success: true,
      chunkResult,
      levelUpResult,
      newBadges: badges,
      session: {
        id: session._id,
        completedChunks: session.completedChunks,
        totalChunks: session.totalChunks,
        status: session.status,
        pointsEarned: session.pointsEarned,
        completionPercentage: session.completionPercentage
      },
      user: {
        totalPoints: user.totalPoints,
        weeklyPoints: user.weeklyPoints,
        currentLevel: user.currentLevel,
        chunkSizeLevel: user.chunkSizeLevel
      }
    });

  } catch (error) {
    console.error('Complete chunk error:', error);
    res.status(500).json({
      error: 'Failed to complete chunk',
      code: 'CHUNK_COMPLETION_ERROR'
    });
  }
});

// Get user's active sessions
router.get('/active-sessions', authenticateToken, async (req, res) => {
  try {
    const sessions = await ContentSession.getActiveSessions(req.userId);
    
    res.json({
      success: true,
      sessions: sessions.map(session => ({
        id: session._id,
        contentTitle: session.contentTitle,
        contentType: session.contentType,
        contentUrl: session.contentUrl,
        totalChunks: session.totalChunks,
        completedChunks: session.completedChunks,
        completionPercentage: session.completionPercentage,
        status: session.status,
        lastAccessedAt: session.lastAccessedAt,
        pointsEarned: session.pointsEarned
      }))
    });

  } catch (error) {
    console.error('Get active sessions error:', error);
    res.status(500).json({
      error: 'Failed to get active sessions',
      code: 'GET_SESSIONS_ERROR'
    });
  }
});

// Get user's completed sessions
router.get('/completed-sessions', authenticateToken, async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const sessions = await ContentSession.getCompletedSessions(req.userId, parseInt(limit));
    
    res.json({
      success: true,
      sessions: sessions.map(session => ({
        id: session._id,
        contentTitle: session.contentTitle,
        contentType: session.contentType,
        totalChunks: session.totalChunks,
        pointsEarned: session.pointsEarned,
        completedAt: session.completedAt,
        totalTimeSpent: session.totalTimeSpent,
        averageTimePerChunk: session.averageTimePerChunk,
        userRating: session.userRating
      }))
    });

  } catch (error) {
    console.error('Get completed sessions error:', error);
    res.status(500).json({
      error: 'Failed to get completed sessions',
      code: 'GET_COMPLETED_SESSIONS_ERROR'
    });
  }
});

// Pause a session
router.post('/pause-session/:sessionId', authenticateToken, async (req, res) => {
  try {
    const session = await ContentSession.findOne({
      _id: req.params.sessionId,
      userId: req.userId,
      status: 'active'
    });

    if (!session) {
      return res.status(404).json({
        error: 'Active session not found',
        code: 'SESSION_NOT_FOUND'
      });
    }

    session.pauseSession();
    await session.save();

    res.json({
      success: true,
      message: 'Session paused',
      session: {
        id: session._id,
        status: session.status
      }
    });

  } catch (error) {
    console.error('Pause session error:', error);
    res.status(500).json({
      error: 'Failed to pause session',
      code: 'PAUSE_SESSION_ERROR'
    });
  }
});

// Resume a session
router.post('/resume-session/:sessionId', authenticateToken, async (req, res) => {
  try {
    const session = await ContentSession.findOne({
      _id: req.params.sessionId,
      userId: req.userId,
      status: 'paused'
    });

    if (!session) {
      return res.status(404).json({
        error: 'Paused session not found',
        code: 'SESSION_NOT_FOUND'
      });
    }

    session.resumeSession();
    await session.save();

    res.json({
      success: true,
      message: 'Session resumed',
      session: {
        id: session._id,
        status: session.status
      }
    });

  } catch (error) {
    console.error('Resume session error:', error);
    res.status(500).json({
      error: 'Failed to resume session',
      code: 'RESUME_SESSION_ERROR'
    });
  }
});

// Rate a completed session
router.post('/rate-session/:sessionId', authenticateToken, async (req, res) => {
  try {
    const { rating, feedback } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        error: 'Rating must be between 1 and 5',
        code: 'INVALID_RATING'
      });
    }

    const session = await ContentSession.findOne({
      _id: req.params.sessionId,
      userId: req.userId,
      status: 'completed'
    });

    if (!session) {
      return res.status(404).json({
        error: 'Completed session not found',
        code: 'SESSION_NOT_FOUND'
      });
    }

    session.userRating = rating;
    if (feedback) {
      session.userFeedback = feedback.substring(0, 500);
    }

    await session.save();

    res.json({
      success: true,
      message: 'Session rated successfully'
    });

  } catch (error) {
    console.error('Rate session error:', error);
    res.status(500).json({
      error: 'Failed to rate session',
      code: 'RATE_SESSION_ERROR'
    });
  }
});

// Helper method to filter content by parent restrictions
router.filterContentByRestrictions = function(content, restrictions) {
  const filtered = { ...content };
  
  // Filter by allowed content types
  Object.keys(filtered).forEach(key => {
    if (key.endsWith('s') && Array.isArray(filtered[key])) {
      const contentType = key.slice(0, -1); // Remove 's' from end
      if (!restrictions.allowedContentTypes.includes(contentType)) {
        filtered[key] = [];
      } else {
        // Filter by blocked topics
        filtered[key] = filtered[key].filter(item => {
          return !restrictions.blockedTopics.some(blockedTopic =>
            item.title.toLowerCase().includes(blockedTopic.toLowerCase()) ||
            item.description.toLowerCase().includes(blockedTopic.toLowerCase()) ||
            item.topics.some(topic => topic.toLowerCase().includes(blockedTopic.toLowerCase()))
          );
        });
      }
    }
  });
  
  return filtered;
};

// Helper method to check for new badges
router.checkForNewBadges = async function(user, session) {
  const newBadges = [];
  
  // First completion badge
  if (user.totalPoints >= 10 && !user.badges.some(b => b.badgeId === 'first_completion')) {
    const badge = { badgeId: 'first_completion', name: 'First Steps', earnedAt: new Date() };
    user.badges.push(badge);
    newBadges.push(badge);
  }
  
  // Content type specific badges
  const contentTypeBadges = {
    article: { id: 'news_reader', name: 'News Explorer', threshold: 5 },
    video: { id: 'video_watcher', name: 'Video Scholar', threshold: 5 },
    book: { id: 'book_reader', name: 'Book Lover', threshold: 5 }
  };
  
  const typeCompletions = await ContentSession.countDocuments({
    userId: user._id,
    contentType: session.contentType,
    status: 'completed'
  });
  
  const typeBadge = contentTypeBadges[session.contentType];
  if (typeBadge && typeCompletions >= typeBadge.threshold && 
      !user.badges.some(b => b.badgeId === typeBadge.id)) {
    const badge = { badgeId: typeBadge.id, name: typeBadge.name, earnedAt: new Date() };
    user.badges.push(badge);
    newBadges.push(badge);
  }
  
  // Point collector badges
  const pointBadges = [
    { threshold: 100, id: 'point_collector_100', name: 'Point Collector' },
    { threshold: 500, id: 'point_collector_500', name: 'Point Master' },
    { threshold: 1000, id: 'point_collector_1000', name: 'Point Champion' }
  ];
  
  pointBadges.forEach(pointBadge => {
    if (user.totalPoints >= pointBadge.threshold && 
        !user.badges.some(b => b.badgeId === pointBadge.id)) {
      const badge = { badgeId: pointBadge.id, name: pointBadge.name, earnedAt: new Date() };
      user.badges.push(badge);
      newBadges.push(badge);
    }
  });
  
  return newBadges;
};

module.exports = router;
