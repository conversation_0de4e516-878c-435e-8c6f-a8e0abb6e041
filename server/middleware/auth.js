const jwt = require('jsonwebtoken');
const { User, ParentAccount } = require('../models');

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
      return res.status(401).json({ 
        error: 'Access token required',
        code: 'NO_TOKEN'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await User.findById(decoded.userId).select('-password');
    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid token - user not found',
        code: 'INVALID_TOKEN'
      });
    }
    
    req.user = user;
    req.userId = user._id;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({ 
      error: 'Authentication error',
      code: 'AUTH_ERROR'
    });
  }
};

// Middleware to verify parent token
const authenticateParent = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ 
        error: 'Parent access token required',
        code: 'NO_PARENT_TOKEN'
      });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'parent') {
      return res.status(401).json({ 
        error: 'Parent token required',
        code: 'INVALID_PARENT_TOKEN'
      });
    }
    
    // Get parent account from database
    const parentAccount = await ParentAccount.findById(decoded.parentId)
      .populate('childUserId', '-password');
    
    if (!parentAccount) {
      return res.status(401).json({ 
        error: 'Invalid parent token',
        code: 'INVALID_PARENT_TOKEN'
      });
    }
    
    req.parentAccount = parentAccount;
    req.childUser = parentAccount.childUserId;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid parent token',
        code: 'INVALID_PARENT_TOKEN'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Parent token expired',
        code: 'PARENT_TOKEN_EXPIRED'
      });
    }
    
    console.error('Parent auth middleware error:', error);
    res.status(500).json({ 
      error: 'Parent authentication error',
      code: 'PARENT_AUTH_ERROR'
    });
  }
};

// Middleware to check if user is within age limits
const checkAgeRestrictions = (req, res, next) => {
  const user = req.user;
  const age = Math.floor((Date.now() - user.dateOfBirth.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
  
  if (age < 6 || age > 16) {
    return res.status(403).json({ 
      error: 'Age restrictions apply',
      code: 'AGE_RESTRICTED'
    });
  }
  
  req.userAge = age;
  next();
};

// Middleware to check content restrictions (for parent-controlled accounts)
const checkContentRestrictions = async (req, res, next) => {
  try {
    const parentAccount = await ParentAccount.findOne({ childUserId: req.userId });
    
    if (parentAccount) {
      req.contentRestrictions = parentAccount.contentRestrictions;
      req.hasParentAccount = true;
    } else {
      req.hasParentAccount = false;
    }
    
    next();
  } catch (error) {
    console.error('Content restrictions check error:', error);
    next(); // Continue without restrictions if error occurs
  }
};

// Middleware to validate content type access
const validateContentAccess = (req, res, next) => {
  const { contentType } = req.body;
  
  if (req.contentRestrictions && !req.contentRestrictions.allowedContentTypes.includes(contentType)) {
    return res.status(403).json({ 
      error: `Content type '${contentType}' is not allowed by parent settings`,
      code: 'CONTENT_TYPE_RESTRICTED'
    });
  }
  
  next();
};

// Middleware to check daily screen time limits
const checkScreenTimeLimit = async (req, res, next) => {
  try {
    if (!req.hasParentAccount) {
      return next();
    }
    
    const parentAccount = await ParentAccount.findOne({ childUserId: req.userId });
    if (!parentAccount || !parentAccount.contentRestrictions.maxDailyScreenTime) {
      return next();
    }
    
    // Calculate today's screen time
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    const ContentSession = require('../models/ContentSession');
    const todaySessions = await ContentSession.find({
      userId: req.userId,
      createdAt: { $gte: today, $lt: tomorrow }
    });
    
    const totalScreenTime = todaySessions.reduce((total, session) => {
      return total + (session.totalTimeSpent || 0);
    }, 0);
    
    const maxScreenTimeSeconds = parentAccount.contentRestrictions.maxDailyScreenTime * 60;
    
    if (totalScreenTime >= maxScreenTimeSeconds) {
      return res.status(429).json({ 
        error: 'Daily screen time limit reached',
        code: 'SCREEN_TIME_LIMIT',
        data: {
          usedTime: Math.round(totalScreenTime / 60),
          maxTime: parentAccount.contentRestrictions.maxDailyScreenTime
        }
      });
    }
    
    req.remainingScreenTime = maxScreenTimeSeconds - totalScreenTime;
    next();
  } catch (error) {
    console.error('Screen time check error:', error);
    next(); // Continue without screen time check if error occurs
  }
};

// Generate JWT token for user
const generateUserToken = (userId) => {
  return jwt.sign(
    { userId, type: 'user' },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Generate JWT token for parent
const generateParentToken = (parentId, childUserId) => {
  return jwt.sign(
    { parentId, childUserId, type: 'parent' },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

module.exports = {
  authenticateToken,
  authenticateParent,
  checkAgeRestrictions,
  checkContentRestrictions,
  validateContentAccess,
  checkScreenTimeLimit,
  generateUserToken,
  generateParentToken
};
