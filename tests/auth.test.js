const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server/server');
const { User, ParentAccount } = require('../server/models');

describe('Authentication Routes', () => {
  let server;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/brainripe_test';
    await mongoose.connect(mongoUri);
    
    // Start server
    server = app.listen(0);
  });

  afterAll(async () => {
    // Clean up
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
    server.close();
  });

  beforeEach(async () => {
    // Clear collections before each test
    await User.deleteMany({});
    await ParentAccount.deleteMany({});
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        username: 'testuser',
        password: 'password123',
        dateOfBirth: '2010-01-01',
        interests: ['science', 'technology']
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.user.username).toBe('testuser');
      expect(response.body.token).toBeDefined();
      expect(response.body.user.password).toBeUndefined();
    });

    it('should reject registration with invalid age', async () => {
      const userData = {
        username: 'testuser',
        password: 'password123',
        dateOfBirth: '2020-01-01', // Too young
        interests: ['science']
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('INVALID_AGE');
    });

    it('should reject registration with duplicate username', async () => {
      const userData = {
        username: 'testuser',
        password: 'password123',
        dateOfBirth: '2010-01-01',
        interests: ['science']
      };

      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Second registration with same username
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body.code).toBe('DUPLICATE_ERROR');
    });

    it('should reject registration with missing required fields', async () => {
      const userData = {
        username: 'testuser'
        // Missing password, dateOfBirth, interests
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/auth/login', () => {
    let testUser;

    beforeEach(async () => {
      // Create test user
      testUser = new User({
        username: 'testuser',
        password: 'password123',
        dateOfBirth: new Date('2010-01-01'),
        interests: ['science', 'technology']
      });
      await testUser.save();
    });

    it('should login successfully with correct credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.user.username).toBe('testuser');
      expect(response.body.token).toBeDefined();
    });

    it('should reject login with incorrect password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });

    it('should reject login with non-existent username', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'nonexistent',
          password: 'password123'
        })
        .expect(401);

      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });
  });

  describe('POST /api/auth/parent/setup', () => {
    let testUser, authToken;

    beforeEach(async () => {
      // Create and login test user
      testUser = new User({
        username: 'testuser',
        password: 'password123',
        dateOfBirth: new Date('2010-01-01'),
        interests: ['science']
      });
      await testUser.save();

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });

      authToken = loginResponse.body.token;
    });

    it('should setup parent account successfully', async () => {
      const parentData = {
        parentEmail: '<EMAIL>',
        parentPassword: 'parentpass123'
      };

      const response = await request(app)
        .post('/api/auth/parent/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(parentData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.parentAccount.parentEmail).toBe('<EMAIL>');
      expect(response.body.parentToken).toBeDefined();
    });

    it('should reject parent setup without authentication', async () => {
      const parentData = {
        parentEmail: '<EMAIL>',
        parentPassword: 'parentpass123'
      };

      const response = await request(app)
        .post('/api/auth/parent/setup')
        .send(parentData)
        .expect(401);

      expect(response.body.code).toBe('NO_TOKEN');
    });

    it('should reject parent setup with invalid email', async () => {
      const parentData = {
        parentEmail: 'invalid-email',
        parentPassword: 'parentpass123'
      };

      const response = await request(app)
        .post('/api/auth/parent/setup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(parentData)
        .expect(400);

      expect(response.body.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/auth/parent/login', () => {
    let testUser, parentAccount;

    beforeEach(async () => {
      // Create test user and parent account
      testUser = new User({
        username: 'testuser',
        password: 'password123',
        dateOfBirth: new Date('2010-01-01'),
        interests: ['science']
      });
      await testUser.save();

      parentAccount = new ParentAccount({
        childUserId: testUser._id,
        parentEmail: '<EMAIL>',
        parentPassword: 'parentpass123'
      });
      await parentAccount.save();
    });

    it('should login parent successfully', async () => {
      const response = await request(app)
        .post('/api/auth/parent/login')
        .send({
          parentEmail: '<EMAIL>',
          parentPassword: 'parentpass123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.parentAccount.parentEmail).toBe('<EMAIL>');
      expect(response.body.childUser.username).toBe('testuser');
      expect(response.body.token).toBeDefined();
    });

    it('should reject parent login with wrong credentials', async () => {
      const response = await request(app)
        .post('/api/auth/parent/login')
        .send({
          parentEmail: '<EMAIL>',
          parentPassword: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });
  });

  describe('GET /api/auth/verify', () => {
    let testUser, authToken;

    beforeEach(async () => {
      testUser = new User({
        username: 'testuser',
        password: 'password123',
        dateOfBirth: new Date('2010-01-01'),
        interests: ['science']
      });
      await testUser.save();

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'password123'
        });

      authToken = loginResponse.body.token;
    });

    it('should verify valid token', async () => {
      const response = await request(app)
        .get('/api/auth/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.user.username).toBe('testuser');
    });

    it('should reject invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/verify')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.code).toBe('INVALID_TOKEN');
    });

    it('should reject missing token', async () => {
      const response = await request(app)
        .get('/api/auth/verify')
        .expect(401);

      expect(response.body.code).toBe('NO_TOKEN');
    });
  });
});
