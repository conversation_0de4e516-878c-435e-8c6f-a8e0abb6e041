#!/bin/bash

# BrainRipe Deployment Script
# Usage: ./scripts/deploy.sh [environment] [options]

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
BrainRipe Deployment Script

Usage: $0 [environment] [options]

Environments:
    development     Deploy for development with hot reload
    production      Deploy for production with optimizations
    test           Deploy for testing

Options:
    --build        Force rebuild of Docker images
    --clean        Clean up old containers and images
    --logs         Show logs after deployment
    --help         Show this help message

Examples:
    $0 production --build
    $0 development --logs
    $0 test --clean --build

EOF
}

# Parse command line arguments
BUILD_FLAG=""
CLEAN_FLAG=""
SHOW_LOGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            BUILD_FLAG="--build"
            shift
            ;;
        --clean)
            CLEAN_FLAG="true"
            shift
            ;;
        --logs)
            SHOW_LOGS="true"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        development|production|test)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|production|test)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT"
    log_info "Valid environments: development, production, test"
    exit 1
fi

log_info "Starting deployment for environment: $ENVIRONMENT"

# Change to project directory
cd "$PROJECT_DIR"

# Check if required files exist
if [[ ! -f "$ENV_FILE" ]]; then
    log_error "Environment file $ENV_FILE not found!"
    log_info "Please copy .env.example to .env and configure it"
    exit 1
fi

if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
    log_error "Docker Compose file $DOCKER_COMPOSE_FILE not found!"
    exit 1
fi

# Load environment variables
set -a  # Automatically export all variables
source "$ENV_FILE"
set +a

# Validate required environment variables
REQUIRED_VARS=(
    "JWT_SECRET"
    "JWT_PARENT_SECRET"
    "OPENAI_API_KEY"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var}" ]]; then
        log_error "Required environment variable $var is not set!"
        exit 1
    fi
done

# Clean up if requested
if [[ "$CLEAN_FLAG" == "true" ]]; then
    log_info "Cleaning up old containers and images..."
    
    # Stop and remove containers
    docker-compose down --remove-orphans || true
    
    # Remove unused images
    docker image prune -f || true
    
    # Remove unused volumes (be careful with this in production)
    if [[ "$ENVIRONMENT" != "production" ]]; then
        docker volume prune -f || true
    fi
    
    log_success "Cleanup completed"
fi

# Set Docker Compose profiles based on environment
export COMPOSE_PROFILES=""
case "$ENVIRONMENT" in
    development)
        export COMPOSE_PROFILES="development"
        export NODE_ENV="development"
        export BUILD_TARGET="development"
        ;;
    production)
        export COMPOSE_PROFILES="production"
        export NODE_ENV="production"
        export BUILD_TARGET="production"
        ;;
    test)
        export NODE_ENV="test"
        export BUILD_TARGET="test"
        ;;
esac

# Create necessary directories
log_info "Creating necessary directories..."
mkdir -p logs/nginx
mkdir -p nginx/ssl

# Generate SSL certificates for production if they don't exist
if [[ "$ENVIRONMENT" == "production" && ! -f "nginx/ssl/cert.pem" ]]; then
    log_info "Generating self-signed SSL certificates..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/key.pem \
        -out nginx/ssl/cert.pem \
        -subj "/C=US/ST=State/L=City/O=BrainRipe/CN=localhost" || {
        log_warning "Failed to generate SSL certificates. HTTPS will not be available."
    }
fi

# Build and start services
log_info "Building and starting services..."

if [[ "$ENVIRONMENT" == "test" ]]; then
    # For testing, run tests in containers
    docker-compose build $BUILD_FLAG app
    docker-compose run --rm app npm test
    log_success "Tests completed successfully"
else
    # For development and production
    docker-compose up -d $BUILD_FLAG
    
    # Wait for services to be healthy
    log_info "Waiting for services to be ready..."
    
    # Wait for database
    timeout=60
    while ! docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; do
        if [[ $timeout -le 0 ]]; then
            log_error "MongoDB failed to start within timeout"
            exit 1
        fi
        sleep 2
        ((timeout-=2))
    done
    
    # Wait for application
    timeout=60
    while ! curl -f http://localhost:${APP_PORT:-3000}/api/health > /dev/null 2>&1; do
        if [[ $timeout -le 0 ]]; then
            log_error "Application failed to start within timeout"
            exit 1
        fi
        sleep 2
        ((timeout-=2))
    done
    
    log_success "All services are running!"
    
    # Show service information
    log_info "Service URLs:"
    echo "  🚀 Main Application: http://localhost:${APP_PORT:-3000}"
    echo "  👨‍👩‍👧‍👦 Parent Dashboard: http://localhost:${APP_PORT:-3000}/parent"
    echo "  🏥 Health Check: http://localhost:${APP_PORT:-3000}/api/health"
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        echo "  🗄️  Database Admin: http://localhost:${MONGO_EXPRESS_PORT:-8081}"
    fi
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "  🔒 HTTPS: https://localhost:${NGINX_HTTPS_PORT:-443}"
    fi
fi

# Show logs if requested
if [[ "$SHOW_LOGS" == "true" ]]; then
    log_info "Showing application logs (Ctrl+C to exit)..."
    docker-compose logs -f app
fi

log_success "Deployment completed successfully!"

# Show next steps
echo ""
log_info "Next steps:"
echo "  • Check application health: curl http://localhost:${APP_PORT:-3000}/api/health"
echo "  • View logs: docker-compose logs -f"
echo "  • Stop services: docker-compose down"
echo "  • Update services: $0 $ENVIRONMENT --build"
