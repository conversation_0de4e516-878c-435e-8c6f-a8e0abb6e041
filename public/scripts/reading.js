// Reading Session Manager
class ReadingManager {
  constructor() {
    this.currentSession = null;
    this.currentChunks = null;
    this.currentChunkIndex = 0;
    this.sessionStartTime = null;
    this.chunkStartTime = null;
    this.pointsPerChunk = 10;
  }

  async startReading(session, chunkedContent) {
    try {
      this.currentSession = session;
      this.currentChunks = chunkedContent.chunks;
      this.currentChunkIndex = session.completedChunks || 0;
      this.sessionStartTime = new Date();
      
      // Update reading interface
      this.updateReadingInterface();
      this.displayCurrentChunk();
      
    } catch (error) {
      console.error('Start reading error:', error);
      throw error;
    }
  }

  async continueReading(session) {
    try {
      // Load session data and continue from where user left off
      this.currentSession = session;
      this.currentChunkIndex = session.completedChunks || 0;
      
      // We need to get the chunked content for this session
      // This would typically be stored or re-generated
      await this.loadSessionChunks(session);
      
      this.updateReadingInterface();
      this.displayCurrentChunk();
      
    } catch (error) {
      console.error('Continue reading error:', error);
      throw error;
    }
  }

  async loadSessionChunks(session) {
    // In a real implementation, you might store chunks in the database
    // or re-generate them. For now, we'll create a simple fallback
    this.currentChunks = [{
      index: this.currentChunkIndex,
      content: `Continue reading: ${session.contentTitle}`,
      type: session.contentType,
      metadata: { estimatedReadingTime: 5 },
      id: `chunk_${this.currentChunkIndex}_continue`
    }];
  }

  updateReadingInterface() {
    if (!this.currentSession) return;

    // Update header information
    document.getElementById('content-title').textContent = this.currentSession.contentTitle;
    document.getElementById('content-source').textContent = this.currentSession.contentSource || 'Unknown Source';
    
    // Update progress bar
    const progressPercentage = (this.currentChunkIndex / this.currentSession.totalChunks) * 100;
    document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
    document.getElementById('current-chunk').textContent = this.currentChunkIndex + 1;
    document.getElementById('total-chunks').textContent = this.currentSession.totalChunks;
  }

  displayCurrentChunk() {
    if (!this.currentChunks || this.currentChunkIndex >= this.currentChunks.length) {
      this.completeSession();
      return;
    }

    const chunk = this.currentChunks[this.currentChunkIndex];
    const chunkContainer = document.getElementById('chunk-content');
    
    // Record chunk start time
    this.chunkStartTime = new Date();
    
    // Display chunk content based on type
    if (this.currentSession.contentType === 'video') {
      this.displayVideoChunk(chunk, chunkContainer);
    } else {
      this.displayTextChunk(chunk, chunkContainer);
    }

    // Update chunk points display
    const estimatedPoints = this.calculateChunkPoints(chunk);
    document.getElementById('chunk-points').textContent = `+${estimatedPoints} points`;
    
    // Update complete button text
    const completeBtn = document.getElementById('complete-chunk');
    const actionText = this.currentSession.contentType === 'video' ? 'Done Watching!' : 'Done Reading!';
    completeBtn.innerHTML = `${actionText} <span id="chunk-points">+${estimatedPoints} points</span>`;
  }

  displayTextChunk(chunk, container) {
    container.innerHTML = `
      <div class="chunk-header">
        <h3>Chunk ${chunk.index + 1}</h3>
        <div class="chunk-meta">
          <span class="reading-time">📖 ~${chunk.metadata?.estimatedReadingTime || 3} min read</span>
        </div>
      </div>
      <div class="chunk-text">
        ${this.formatChunkContent(chunk.content)}
      </div>
      <div class="chunk-footer">
        <div class="reading-tips">
          <p>💡 <strong>Tip:</strong> Take your time and think about what you're reading!</p>
        </div>
      </div>
    `;
  }

  displayVideoChunk(chunk, container) {
    const videoUrl = this.currentSession.contentUrl;
    const startTime = chunk.metadata?.startTime || 0;
    const endTime = chunk.metadata?.endTime || (startTime + 5);

    container.innerHTML = `
      <div class="chunk-header">
        <h3>Video Segment ${chunk.index + 1}</h3>
        <div class="chunk-meta">
          <span class="watch-time">🎥 ${this.formatTime(startTime)} - ${this.formatTime(endTime)}</span>
        </div>
      </div>
      <div class="video-container">
        <div class="video-instructions">
          <p>Watch the video from <strong>${this.formatTime(startTime)}</strong> to <strong>${this.formatTime(endTime)}</strong></p>
          <a href="${videoUrl}" target="_blank" class="btn btn-secondary video-link">
            🎥 Open Video in New Tab
          </a>
        </div>
        <div class="video-tips">
          <p>💡 <strong>Tip:</strong> Pay attention to the key concepts and take mental notes!</p>
        </div>
      </div>
    `;
  }

  formatChunkContent(content) {
    // Basic formatting for better readability
    return content
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
      .replace(/^/, '<p>')
      .replace(/$/, '</p>');
  }

  formatTime(minutes) {
    const mins = Math.floor(minutes);
    const secs = Math.floor((minutes - mins) * 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  calculateChunkPoints(chunk) {
    const basePoints = 10;
    const difficultyMultiplier = (this.currentSession.difficultyLevel || 5) * 0.2;
    const timeBonus = Math.min((chunk.metadata?.estimatedReadingTime || 3) / 2, 5);
    
    return Math.round(basePoints * (1 + difficultyMultiplier) + timeBonus);
  }

  async completeCurrentChunk() {
    try {
      if (!this.currentSession || !this.chunkStartTime) {
        console.error('No active session or chunk start time');
        return;
      }

      // Calculate time spent on this chunk
      const timeSpent = Math.floor((new Date() - this.chunkStartTime) / 1000);
      
      // Show loading state
      const completeBtn = document.getElementById('complete-chunk');
      const originalText = completeBtn.innerHTML;
      completeBtn.innerHTML = 'Processing... ⏳';
      completeBtn.disabled = true;

      // Call API to complete chunk
      const result = await this.completeChunk(this.currentSession.id, this.currentChunkIndex, timeSpent);
      
      if (result.success) {
        // Show points animation
        this.showPointsAnimation(result.chunkResult.chunkPoints);
        
        // Update user stats if level up occurred
        if (result.levelUpResult.levelUp) {
          this.showLevelUpAnimation(result.levelUpResult.newLevel);
          // Update current user data
          window.brainRipeApp.currentUser.currentLevel = result.levelUpResult.newLevel;
          window.brainRipeApp.currentUser.totalPoints = result.user.totalPoints;
          window.brainRipeApp.currentUser.weeklyPoints = result.user.weeklyPoints;
          window.brainRipeApp.updateUserStats();
        }

        // Show new badges if any
        if (result.newBadges && result.newBadges.length > 0) {
          this.showBadgeAnimation(result.newBadges);
        }

        // Move to next chunk or complete session
        this.currentChunkIndex++;
        
        if (this.currentChunkIndex >= this.currentSession.totalChunks) {
          // Session completed
          this.completeSession(result.session);
        } else {
          // Continue to next chunk
          setTimeout(() => {
            this.displayCurrentChunk();
            this.updateReadingInterface();
          }, 2000); // Give time for animations
        }
      } else {
        throw new Error(result.error || 'Failed to complete chunk');
      }

    } catch (error) {
      console.error('Complete chunk error:', error);
      window.brainRipeApp.showNotification('Failed to complete chunk. Please try again.', 'error');
      
      // Reset button
      const completeBtn = document.getElementById('complete-chunk');
      completeBtn.innerHTML = originalText;
      completeBtn.disabled = false;
    }
  }

  async completeChunk(sessionId, chunkIndex, timeSpent) {
    const token = localStorage.getItem('brainripe_token');
    const response = await fetch(`/api/content/complete-chunk/${sessionId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        chunkIndex,
        timeSpent
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to complete chunk');
    }

    return response.json();
  }

  completeSession(sessionData = null) {
    // Show completion modal
    const modal = document.getElementById('completion-modal');
    modal.classList.remove('hidden');

    // Update completion stats
    if (sessionData) {
      document.getElementById('session-points').textContent = sessionData.pointsEarned || 0;
      const totalTime = Math.floor((new Date() - this.sessionStartTime) / 60000);
      document.getElementById('session-time').textContent = totalTime;
    }

    // Add confetti effect
    this.showConfettiAnimation();

    // Reset session data
    this.currentSession = null;
    this.currentChunks = null;
    this.currentChunkIndex = 0;
    this.sessionStartTime = null;
  }

  async pauseSession() {
    try {
      if (!this.currentSession) return;

      const token = localStorage.getItem('brainripe_token');
      const response = await fetch(`/api/content/pause-session/${this.currentSession.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        window.brainRipeApp.showNotification('Session paused', 'info');
        window.brainRipeApp.showDashboard();
      } else {
        throw new Error('Failed to pause session');
      }
    } catch (error) {
      console.error('Pause session error:', error);
      window.brainRipeApp.showNotification('Failed to pause session', 'error');
    }
  }

  showPointsAnimation(points) {
    const container = document.getElementById('points-animation-container');
    const animation = document.createElement('div');
    animation.className = 'points-animation';
    animation.textContent = `+${points}`;
    
    container.appendChild(animation);
    
    // Animate
    setTimeout(() => {
      animation.classList.add('animate');
    }, 100);
    
    // Remove after animation
    setTimeout(() => {
      animation.remove();
    }, 2000);
  }

  showLevelUpAnimation(newLevel) {
    const container = document.getElementById('points-animation-container');
    const animation = document.createElement('div');
    animation.className = 'level-up-animation';
    animation.innerHTML = `
      <div class="level-up-content">
        <div class="level-up-icon">🎉</div>
        <div class="level-up-text">Level Up!</div>
        <div class="level-up-level">Level ${newLevel}</div>
      </div>
    `;
    
    container.appendChild(animation);
    
    // Animate
    setTimeout(() => {
      animation.classList.add('animate');
    }, 100);
    
    // Remove after animation
    setTimeout(() => {
      animation.remove();
    }, 3000);
  }

  showBadgeAnimation(badges) {
    badges.forEach((badge, index) => {
      setTimeout(() => {
        const container = document.getElementById('points-animation-container');
        const animation = document.createElement('div');
        animation.className = 'badge-animation';
        animation.innerHTML = `
          <div class="badge-content">
            <div class="badge-icon">🏅</div>
            <div class="badge-text">New Badge!</div>
            <div class="badge-name">${badge.name}</div>
          </div>
        `;
        
        container.appendChild(animation);
        
        // Animate
        setTimeout(() => {
          animation.classList.add('animate');
        }, 100);
        
        // Remove after animation
        setTimeout(() => {
          animation.remove();
        }, 3000);
      }, index * 1000); // Stagger multiple badges
    });
  }

  showConfettiAnimation() {
    // Simple confetti effect
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
    const container = document.getElementById('points-animation-container');
    
    for (let i = 0; i < 50; i++) {
      setTimeout(() => {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.animationDelay = Math.random() * 2 + 's';
        
        container.appendChild(confetti);
        
        setTimeout(() => {
          confetti.remove();
        }, 3000);
      }, i * 50);
    }
  }
}
