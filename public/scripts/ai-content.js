// Content Manager - Handles AI content curation and session management
class ContentManager {
  constructor() {
    this.baseUrl = '/api/content';
    this.cachedContent = null;
    this.lastRefresh = null;
  }

  async getCuratedContent(forceRefresh = false) {
    try {
      const token = localStorage.getItem('brainripe_token');
      const url = `${this.baseUrl}/curate${forceRefresh ? '?forceRefresh=true' : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get curated content');
      }

      const data = await response.json();
      
      // Cache the content
      this.cachedContent = data.content;
      this.lastRefresh = new Date();
      
      return data.content;
    } catch (error) {
      console.error('Get curated content error:', error);
      
      // Return cached content if available
      if (this.cachedContent) {
        return this.cachedContent;
      }
      
      // Return fallback content
      return this.getFallbackContent();
    }
  }

  async startSession(contentItem) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/start-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          contentUrl: contentItem.url,
          contentTitle: contentItem.title,
          contentType: contentItem.type,
          contentSource: contentItem.source,
          estimatedDuration: contentItem.estimatedDuration,
          difficultyLevel: contentItem.difficultyLevel,
          content: contentItem.description // Fallback content for chunking
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to start session');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Start session error:', error);
      throw error;
    }
  }

  async getActiveSessions() {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/active-sessions`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get active sessions');
      }

      const data = await response.json();
      return data.sessions || [];
    } catch (error) {
      console.error('Get active sessions error:', error);
      return [];
    }
  }

  async getCompletedSessions(limit = 10) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/completed-sessions?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to get completed sessions');
      }

      const data = await response.json();
      return data.sessions || [];
    } catch (error) {
      console.error('Get completed sessions error:', error);
      return [];
    }
  }

  async pauseSession(sessionId) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/pause-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to pause session');
      }

      return await response.json();
    } catch (error) {
      console.error('Pause session error:', error);
      throw error;
    }
  }

  async resumeSession(sessionId) {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/resume-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to resume session');
      }

      return await response.json();
    } catch (error) {
      console.error('Resume session error:', error);
      throw error;
    }
  }

  async rateSession(sessionId, rating, feedback = '') {
    try {
      const token = localStorage.getItem('brainripe_token');
      
      const response = await fetch(`${this.baseUrl}/rate-session/${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          rating,
          feedback
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to rate session');
      }

      return await response.json();
    } catch (error) {
      console.error('Rate session error:', error);
      throw error;
    }
  }

  // Get fallback content when AI curation fails
  getFallbackContent() {
    return {
      articles: [
        {
          id: 'fallback_article_1',
          title: 'Amazing Animals Around the World',
          url: 'https://www.natgeokids.com/uk/discover/animals/',
          type: 'article',
          source: 'National Geographic Kids',
          estimatedDuration: 5,
          difficultyLevel: 5,
          description: 'Discover incredible facts about animals from around the globe!',
          topics: ['animals', 'nature', 'science']
        },
        {
          id: 'fallback_article_2',
          title: 'Space Exploration for Kids',
          url: 'https://www.nasa.gov/audience/forstudents/k-4/',
          type: 'article',
          source: 'NASA Kids',
          estimatedDuration: 7,
          difficultyLevel: 6,
          description: 'Learn about rockets, planets, and space exploration!',
          topics: ['space', 'science', 'exploration']
        }
      ],
      videos: [
        {
          id: 'fallback_video_1',
          title: 'How Do Plants Grow?',
          url: 'https://www.youtube.com/watch?v=example1',
          type: 'video',
          source: 'SciShow Kids',
          estimatedDuration: 4,
          difficultyLevel: 4,
          description: 'Learn about photosynthesis and how plants make their own food!',
          topics: ['plants', 'science', 'nature']
        },
        {
          id: 'fallback_video_2',
          title: 'The Water Cycle Explained',
          url: 'https://www.youtube.com/watch?v=example2',
          type: 'video',
          source: 'Crash Course Kids',
          estimatedDuration: 6,
          difficultyLevel: 5,
          description: 'Discover how water moves around our planet!',
          topics: ['water', 'weather', 'science']
        }
      ],
      books: [
        {
          id: 'fallback_book_1',
          title: 'The Adventures of Tom Sawyer',
          url: 'https://www.gutenberg.org/ebooks/74',
          type: 'book',
          source: 'Project Gutenberg',
          estimatedDuration: 20,
          difficultyLevel: 7,
          description: 'Join Tom Sawyer on his exciting adventures along the Mississippi River!',
          topics: ['adventure', 'friendship', 'classic']
        },
        {
          id: 'fallback_book_2',
          title: 'Alice\'s Adventures in Wonderland',
          url: 'https://www.gutenberg.org/ebooks/11',
          type: 'book',
          source: 'Project Gutenberg',
          estimatedDuration: 15,
          difficultyLevel: 6,
          description: 'Follow Alice down the rabbit hole into a magical world!',
          topics: ['fantasy', 'adventure', 'imagination']
        }
      ],
      generatedAt: new Date(),
      isFallback: true
    };
  }

  // Filter content by type
  filterContentByType(content, type) {
    if (!content) return [];
    
    switch (type) {
      case 'article':
        return content.articles || [];
      case 'video':
        return content.videos || [];
      case 'book':
        return content.books || [];
      default:
        return [];
    }
  }

  // Get content recommendations based on user interests
  getRecommendedContent(content, userInterests = []) {
    if (!content || userInterests.length === 0) return content;

    const allContent = [
      ...(content.articles || []),
      ...(content.videos || []),
      ...(content.books || [])
    ];

    // Score content based on interest matching
    const scoredContent = allContent.map(item => {
      let score = 0;
      userInterests.forEach(interest => {
        if (item.topics.some(topic => 
          topic.toLowerCase().includes(interest.toLowerCase()) ||
          interest.toLowerCase().includes(topic.toLowerCase())
        )) {
          score += 1;
        }
        if (item.title.toLowerCase().includes(interest.toLowerCase()) ||
            item.description.toLowerCase().includes(interest.toLowerCase())) {
          score += 0.5;
        }
      });
      return { ...item, score };
    });

    // Sort by score and return top recommendations
    const recommended = scoredContent
      .sort((a, b) => b.score - a.score)
      .slice(0, 6);

    return {
      ...content,
      recommended
    };
  }

  // Check if content needs refresh
  needsRefresh() {
    if (!this.lastRefresh) return true;
    
    const hoursSinceRefresh = (Date.now() - this.lastRefresh.getTime()) / (1000 * 60 * 60);
    return hoursSinceRefresh >= 24; // Refresh every 24 hours
  }

  // Clear cached content
  clearCache() {
    this.cachedContent = null;
    this.lastRefresh = null;
  }
}
