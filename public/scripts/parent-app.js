// Parent Dashboard Application
class ParentApp {
  constructor() {
    this.parentAccount = null;
    this.childUser = null;
    this.authManager = new ParentAuthManager();
    this.dashboardManager = new ParentDashboardManager();
    
    this.init();
  }

  async init() {
    try {
      // Show loading screen
      this.showScreen('loading');
      
      // Check for existing parent authentication
      const token = localStorage.getItem('brainripe_parent_token');
      if (token) {
        const isValid = await this.authManager.verifyParentToken(token);
        if (isValid) {
          this.parentAccount = isValid.parentAccount;
          this.childUser = isValid.childUser;
          await this.showDashboard();
          return;
        } else {
          localStorage.removeItem('brainripe_parent_token');
        }
      }
      
      // Show parent authentication screen
      this.showScreen('parent-auth');
      this.setupEventListeners();
      
    } catch (error) {
      console.error('Parent app initialization error:', error);
      this.showNotification('Failed to initialize parent dashboard', 'error');
      this.showScreen('parent-auth');
    }
  }

  setupEventListeners() {
    // Parent login form
    document.getElementById('parent-login-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleParentLogin();
    });

    // Dashboard controls
    document.getElementById('parent-logout-btn').addEventListener('click', () => {
      this.handleLogout();
    });

    document.getElementById('edit-goals-btn').addEventListener('click', () => {
      this.showGoalsModal();
    });

    document.getElementById('edit-restrictions-btn').addEventListener('click', () => {
      this.showRestrictionsModal();
    });

    document.getElementById('settings-btn').addEventListener('click', () => {
      this.showSettingsModal();
    });

    // Goals modal
    document.getElementById('save-goals').addEventListener('click', () => {
      this.saveGoals();
    });

    document.getElementById('cancel-goals').addEventListener('click', () => {
      this.hideModal('goals-modal');
    });

    // Activity filter
    document.getElementById('activity-filter').addEventListener('change', (e) => {
      this.filterActivity(e.target.value);
    });

    // Chart controls
    document.querySelectorAll('.chart-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchChartPeriod(e.target.dataset.period);
      });
    });

    // Modal close buttons
    document.querySelectorAll('.modal-close').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const modal = e.target.closest('.modal');
        if (modal) {
          modal.classList.add('hidden');
        }
      });
    });
  }

  showScreen(screenName) {
    // Hide all screens
    document.querySelectorAll('.screen').forEach(screen => {
      screen.classList.add('hidden');
    });

    // Show loading screen immediately
    if (screenName === 'loading') {
      document.getElementById('loading-screen').classList.remove('hidden');
      return;
    }

    // Hide loading screen
    document.getElementById('loading-screen').classList.add('hidden');

    // Show requested screen
    const targetScreen = document.getElementById(`${screenName}-screen`);
    if (targetScreen) {
      targetScreen.classList.remove('hidden');
    }
  }

  async handleParentLogin() {
    try {
      const email = document.getElementById('parent-email').value.trim();
      const password = document.getElementById('parent-password').value;

      if (!email || !password) {
        this.showNotification('Please fill in all fields', 'error');
        return;
      }

      this.showLoading('Logging in...');

      const result = await this.authManager.parentLogin(email, password);
      if (result.success) {
        this.parentAccount = result.parentAccount;
        this.childUser = result.childUser;
        localStorage.setItem('brainripe_parent_token', result.token);
        this.showNotification('Welcome to the parent dashboard!', 'success');
        await this.showDashboard();
      } else {
        this.showNotification(result.error || 'Login failed', 'error');
      }
    } catch (error) {
      console.error('Parent login error:', error);
      this.showNotification('Login failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async showDashboard() {
    try {
      this.showScreen('parent-dashboard');
      
      // Update header with child info
      document.getElementById('child-name').textContent = this.childUser.username;
      document.getElementById('child-level').textContent = this.childUser.currentLevel;
      
      // Load dashboard data
      await this.dashboardManager.loadDashboard(this.parentAccount, this.childUser);
      
    } catch (error) {
      console.error('Dashboard loading error:', error);
      this.showNotification('Failed to load dashboard', 'error');
    }
  }

  showGoalsModal() {
    const modal = document.getElementById('goals-modal');
    
    // Populate current values
    document.getElementById('weekly-goal-input').value = this.parentAccount.weeklyGoalPoints;
    document.getElementById('monthly-goal-input').value = this.parentAccount.monthlyGoalPoints;
    document.getElementById('screen-time-input').value = this.parentAccount.contentRestrictions.maxDailyScreenTime;
    
    modal.classList.remove('hidden');
  }

  async saveGoals() {
    try {
      const weeklyGoal = parseInt(document.getElementById('weekly-goal-input').value);
      const monthlyGoal = parseInt(document.getElementById('monthly-goal-input').value);
      const screenTimeLimit = parseInt(document.getElementById('screen-time-input').value);

      this.showLoading('Saving goals...');

      const result = await this.dashboardManager.updateGoals(weeklyGoal, monthlyGoal, screenTimeLimit);
      
      if (result.success) {
        this.parentAccount.weeklyGoalPoints = weeklyGoal;
        this.parentAccount.monthlyGoalPoints = monthlyGoal;
        this.parentAccount.contentRestrictions.maxDailyScreenTime = screenTimeLimit;
        
        this.showNotification('Goals updated successfully!', 'success');
        this.hideModal('goals-modal');
        
        // Refresh dashboard data
        await this.dashboardManager.loadDashboard(this.parentAccount, this.childUser);
      } else {
        this.showNotification(result.error || 'Failed to update goals', 'error');
      }
    } catch (error) {
      console.error('Save goals error:', error);
      this.showNotification('Failed to save goals', 'error');
    } finally {
      this.hideLoading();
    }
  }

  showRestrictionsModal() {
    // Create and show restrictions modal
    const modal = this.createRestrictionsModal();
    document.body.appendChild(modal);
    modal.classList.remove('hidden');
  }

  createRestrictionsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal restrictions-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Content Restrictions</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="restriction-section">
            <h4>Allowed Content Types</h4>
            <div class="content-type-toggles">
              <label class="toggle-label">
                <input type="checkbox" value="article" ${this.parentAccount.contentRestrictions.allowedContentTypes.includes('article') ? 'checked' : ''}>
                <span class="toggle-slider"></span>
                📰 Articles & News
              </label>
              <label class="toggle-label">
                <input type="checkbox" value="video" ${this.parentAccount.contentRestrictions.allowedContentTypes.includes('video') ? 'checked' : ''}>
                <span class="toggle-slider"></span>
                🎥 Educational Videos
              </label>
              <label class="toggle-label">
                <input type="checkbox" value="book" ${this.parentAccount.contentRestrictions.allowedContentTypes.includes('book') ? 'checked' : ''}>
                <span class="toggle-slider"></span>
                📚 Books & Stories
              </label>
            </div>
          </div>
          
          <div class="restriction-section">
            <h4>Blocked Topics</h4>
            <div class="blocked-topics-input">
              <input type="text" id="blocked-topic-input" placeholder="Enter topic to block">
              <button type="button" id="add-blocked-topic" class="btn btn-secondary">Add</button>
            </div>
            <div class="blocked-topics-list" id="blocked-topics-list">
              ${this.parentAccount.contentRestrictions.blockedTopics.map(topic => 
                `<span class="blocked-topic-tag">${topic} <button type="button" class="remove-topic" data-topic="${topic}">×</button></span>`
              ).join('')}
            </div>
          </div>
          
          <div class="restriction-section">
            <h4>Social Features</h4>
            <label class="toggle-label">
              <input type="checkbox" id="allow-friends-toggle" ${this.parentAccount.contentRestrictions.allowFriends ? 'checked' : ''}>
              <span class="toggle-slider"></span>
              Allow friend requests and social features
            </label>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="cancel-restrictions">Cancel</button>
          <button class="btn btn-primary" id="save-restrictions">Save Restrictions</button>
        </div>
      </div>
    `;

    // Add event listeners
    modal.querySelector('.modal-close').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#cancel-restrictions').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('#save-restrictions').addEventListener('click', () => {
      this.saveRestrictions(modal);
    });

    modal.querySelector('#add-blocked-topic').addEventListener('click', () => {
      this.addBlockedTopic(modal);
    });

    modal.querySelectorAll('.remove-topic').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.removeBlockedTopic(e.target.dataset.topic, modal);
      });
    });

    return modal;
  }

  async saveRestrictions(modal) {
    try {
      const allowedContentTypes = Array.from(modal.querySelectorAll('.content-type-toggles input:checked'))
        .map(input => input.value);
      
      const blockedTopics = Array.from(modal.querySelectorAll('.blocked-topic-tag'))
        .map(tag => tag.textContent.trim().replace('×', ''));
      
      const allowFriends = modal.querySelector('#allow-friends-toggle').checked;

      this.showLoading('Saving restrictions...');

      const result = await this.dashboardManager.updateRestrictions(allowedContentTypes, blockedTopics, allowFriends);
      
      if (result.success) {
        this.parentAccount.contentRestrictions = result.restrictions;
        this.showNotification('Content restrictions updated!', 'success');
        modal.remove();
      } else {
        this.showNotification(result.error || 'Failed to update restrictions', 'error');
      }
    } catch (error) {
      console.error('Save restrictions error:', error);
      this.showNotification('Failed to save restrictions', 'error');
    } finally {
      this.hideLoading();
    }
  }

  addBlockedTopic(modal) {
    const input = modal.querySelector('#blocked-topic-input');
    const topic = input.value.trim().toLowerCase();
    
    if (!topic) return;
    
    const list = modal.querySelector('#blocked-topics-list');
    const existingTopics = Array.from(list.querySelectorAll('.blocked-topic-tag'))
      .map(tag => tag.textContent.trim().replace('×', '').toLowerCase());
    
    if (existingTopics.includes(topic)) {
      this.showNotification('Topic already blocked', 'warning');
      return;
    }
    
    const topicTag = document.createElement('span');
    topicTag.className = 'blocked-topic-tag';
    topicTag.innerHTML = `${topic} <button type="button" class="remove-topic" data-topic="${topic}">×</button>`;
    
    topicTag.querySelector('.remove-topic').addEventListener('click', (e) => {
      this.removeBlockedTopic(e.target.dataset.topic, modal);
    });
    
    list.appendChild(topicTag);
    input.value = '';
  }

  removeBlockedTopic(topic, modal) {
    const list = modal.querySelector('#blocked-topics-list');
    const topicTag = Array.from(list.querySelectorAll('.blocked-topic-tag'))
      .find(tag => tag.textContent.trim().replace('×', '').toLowerCase() === topic.toLowerCase());
    
    if (topicTag) {
      topicTag.remove();
    }
  }

  showSettingsModal() {
    // Implementation for settings modal
    this.showNotification('Settings modal coming soon!', 'info');
  }

  async filterActivity(filter) {
    try {
      await this.dashboardManager.loadActivity(filter);
    } catch (error) {
      console.error('Filter activity error:', error);
      this.showNotification('Failed to filter activity', 'error');
    }
  }

  async switchChartPeriod(period) {
    try {
      // Update active button
      document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-period="${period}"]`).classList.add('active');
      
      // Load new chart data
      await this.dashboardManager.loadCharts(period);
    } catch (error) {
      console.error('Switch chart period error:', error);
      this.showNotification('Failed to load chart data', 'error');
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  handleLogout() {
    localStorage.removeItem('brainripe_parent_token');
    this.parentAccount = null;
    this.childUser = null;
    this.showScreen('parent-auth');
    this.showNotification('Logged out successfully', 'success');
  }

  showLoading(message = 'Loading...') {
    console.log('Loading:', message);
  }

  hideLoading() {
    console.log('Loading complete');
  }

  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <span class="notification-icon">${icons[type]}</span>
      <span class="notification-message">${message}</span>
      <button class="notification-close">×</button>
    `;

    notification.querySelector('.notification-close').addEventListener('click', () => {
      notification.remove();
    });

    container.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }
}

// Initialize the parent app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.parentApp = new ParentApp();
});
