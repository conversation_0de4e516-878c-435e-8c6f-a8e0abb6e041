// Main Application Controller
console.log('📜 app.js loaded successfully');

class BrainRipeApp {
  constructor() {
    this.currentUser = null;
    this.currentSession = null;

    // Check if all required classes are available
    if (typeof AuthManager === 'undefined') {
      console.error('❌ AuthManager class not found');
      return;
    }
    if (typeof DashboardManager === 'undefined') {
      console.error('❌ DashboardManager class not found');
      return;
    }
    if (typeof ReadingManager === 'undefined') {
      console.error('❌ ReadingManager class not found');
      return;
    }
    if (typeof ContentManager === 'undefined') {
      console.error('❌ ContentManager class not found');
      return;
    }

    this.authManager = new AuthManager();
    this.dashboardManager = new DashboardManager();
    this.readingManager = new ReadingManager();
    this.contentManager = new ContentManager();

    // Add status indicator
    this.updateLoadingStatus('Initializing app...');

    this.init();
  }

  updateLoadingStatus(message) {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      const statusElement = loadingScreen.querySelector('.loading-status') ||
        (() => {
          const status = document.createElement('p');
          status.className = 'loading-status';
          status.style.marginTop = '20px';
          status.style.fontSize = '14px';
          status.style.color = '#666';
          loadingScreen.querySelector('.loading-content').appendChild(status);
          return status;
        })();
      statusElement.textContent = message;
    }
  }

  async init() {
    try {
      console.log('🚀 BrainRipe app initializing...');
      this.updateLoadingStatus('Starting up...');

      // Show loading screen
      console.log('📱 About to show loading screen');
      this.showScreen('loading');
      console.log('📱 Loading screen shown');
      this.updateLoadingStatus('Loading screen displayed');

      // Check for existing authentication
      const token = localStorage.getItem('brainripe_token');
      console.log('🔑 Checking for existing token:', token ? 'Found' : 'Not found');
      this.updateLoadingStatus('Checking authentication...');

      // Temporarily skip token verification for debugging
      if (token && false) { // Disabled for debugging
        console.log('🔍 Verifying token...');
        this.updateLoadingStatus('Verifying token...');
        const isValid = await this.authManager.verifyToken(token);
        console.log('✅ Token verification result:', isValid);

        if (isValid) {
          this.currentUser = isValid.user;
          console.log('👤 User loaded:', this.currentUser.username);
          this.updateLoadingStatus('Loading dashboard...');
          await this.showDashboard();
          return;
        } else {
          console.log('❌ Token invalid, removing...');
          localStorage.removeItem('brainripe_token');
        }
      }

      // Show authentication screen
      console.log('🔐 Showing authentication screen');
      this.updateLoadingStatus('Preparing login screen...');

      setTimeout(() => {
        this.showScreen('auth');
        this.setupEventListeners();
        console.log('✅ App initialization complete');
      }, 500); // Small delay to show the status

      // Fallback: Show auth screen after 3 seconds if still on loading
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen && !loadingScreen.classList.contains('hidden')) {
          console.log('⚠️ Fallback: Forcing auth screen display');
          this.showScreen('auth');
        }
      }, 3000);

    } catch (error) {
      console.error('❌ App initialization error:', error);
      this.updateLoadingStatus('Error: ' + error.message);
      this.showNotification('Failed to initialize app', 'error');
      setTimeout(() => this.showScreen('auth'), 1000);
    }
  }

  setupEventListeners() {
    // Authentication form listeners
    document.getElementById('show-register').addEventListener('click', (e) => {
      e.preventDefault();
      this.showRegisterForm();
    });

    document.getElementById('show-login').addEventListener('click', (e) => {
      e.preventDefault();
      this.showLoginForm();
    });

    document.getElementById('login-form-element').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    document.getElementById('register-form-element').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // Dashboard listeners
    document.getElementById('logout-btn').addEventListener('click', () => {
      this.handleLogout();
    });

    document.getElementById('refresh-content').addEventListener('click', () => {
      this.refreshContent();
    });

    document.getElementById('back-to-dashboard').addEventListener('click', () => {
      this.showDashboard();
    });

    // Content category listeners
    document.querySelectorAll('.category-card').forEach(card => {
      card.addEventListener('click', () => {
        const contentType = card.dataset.type;
        this.showContentType(contentType);
      });
    });

    // Reading interface listeners
    document.getElementById('complete-chunk').addEventListener('click', () => {
      this.readingManager.completeCurrentChunk();
    });

    document.getElementById('pause-session').addEventListener('click', () => {
      this.readingManager.pauseSession();
    });

    document.getElementById('back-to-hub').addEventListener('click', () => {
      this.showDashboard();
    });
  }

  showScreen(screenName) {
    console.log(`🖥️ Showing screen: ${screenName}`);

    // Hide all screens first
    const allScreens = document.querySelectorAll('.screen');
    console.log(`📱 Found ${allScreens.length} screens to hide`);
    allScreens.forEach(screen => {
      screen.classList.add('hidden');
      console.log(`🙈 Hidden screen: ${screen.id}`);
    });

    // Show the requested screen
    const targetScreen = document.getElementById(`${screenName}-screen`);
    if (targetScreen) {
      targetScreen.classList.remove('hidden');
      console.log(`✅ ${screenName} screen displayed (${targetScreen.id})`);

      // Verify the screen is actually visible
      const isHidden = targetScreen.classList.contains('hidden');
      console.log(`🔍 Screen ${screenName} hidden class: ${isHidden}`);
    } else {
      console.error(`❌ Screen element not found: ${screenName}-screen`);
      console.log('🔍 Available screen elements:');
      document.querySelectorAll('[id$="-screen"]').forEach(el => {
        console.log(`  - ${el.id}`);
      });
    }
  }

  showLoginForm() {
    document.getElementById('login-form').classList.remove('hidden');
    document.getElementById('register-form').classList.add('hidden');
  }

  showRegisterForm() {
    document.getElementById('register-form').classList.remove('hidden');
    document.getElementById('login-form').classList.add('hidden');
  }

  async handleLogin() {
    try {
      const username = document.getElementById('login-username').value.trim();
      const password = document.getElementById('login-password').value;

      if (!username || !password) {
        this.showNotification('Please fill in all fields', 'error');
        return;
      }

      this.showLoading('Logging in...');

      const result = await this.authManager.login(username, password);
      if (result.success) {
        this.currentUser = result.user;
        localStorage.setItem('brainripe_token', result.token);
        this.showNotification(`Welcome back, ${result.user.username}!`, 'success');
        await this.showDashboard();
      } else {
        this.showNotification(result.error || 'Login failed', 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showNotification('Login failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async handleRegister() {
    try {
      const username = document.getElementById('register-username').value.trim();
      const password = document.getElementById('register-password').value;
      const birthdate = document.getElementById('register-birthdate').value;
      
      // Get selected interests
      const interests = Array.from(document.querySelectorAll('.interest-item input:checked'))
        .map(input => input.value);

      if (!username || !password || !birthdate) {
        this.showNotification('Please fill in all required fields', 'error');
        return;
      }

      // Validate age
      const age = this.calculateAge(new Date(birthdate));
      if (age < 6 || age > 16) {
        this.showNotification('Age must be between 6 and 16 years old', 'error');
        return;
      }

      this.showLoading('Creating your account...');

      const result = await this.authManager.register(username, password, birthdate, interests);
      if (result.success) {
        this.currentUser = result.user;
        localStorage.setItem('brainripe_token', result.token);
        this.showNotification(`Welcome to BrainRipe, ${result.user.username}!`, 'success');
        await this.showDashboard();
      } else {
        this.showNotification(result.error || 'Registration failed', 'error');
      }
    } catch (error) {
      console.error('Registration error:', error);
      this.showNotification('Registration failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async showDashboard() {
    try {
      this.showScreen('dashboard');
      
      // Update user stats in header
      this.updateUserStats();
      
      // Load dashboard content
      await this.dashboardManager.loadDashboard(this.currentUser);
      
      // Load curated content
      await this.loadCuratedContent();
      
      // Load active sessions
      await this.loadActiveSessions();
      
    } catch (error) {
      console.error('Dashboard loading error:', error);
      this.showNotification('Failed to load dashboard', 'error');
    }
  }

  updateUserStats() {
    if (!this.currentUser) return;

    document.getElementById('user-points').textContent = this.currentUser.totalPoints || 0;
    document.getElementById('user-level').textContent = this.currentUser.currentLevel || 1;
    document.getElementById('weekly-progress').textContent = 
      `${this.currentUser.weeklyPoints || 0}/${this.currentUser.weeklyGoal || 100}`;
  }

  async loadCuratedContent() {
    try {
      const content = await this.contentManager.getCuratedContent();
      this.displayCuratedContent(content);
    } catch (error) {
      console.error('Failed to load curated content:', error);
      this.showNotification('Failed to load content', 'error');
    }
  }

  displayCuratedContent(content) {
    // Update category counts
    document.getElementById('article-count').textContent = 
      `${content.articles?.length || 0} available`;
    document.getElementById('video-count').textContent = 
      `${content.videos?.length || 0} available`;
    document.getElementById('book-count').textContent = 
      `${content.books?.length || 0} available`;

    // Display content items
    const contentList = document.getElementById('content-list');
    contentList.innerHTML = '';

    // Combine all content types
    const allContent = [
      ...(content.articles || []),
      ...(content.videos || []),
      ...(content.books || [])
    ];

    allContent.forEach(item => {
      const contentCard = this.createContentCard(item);
      contentList.appendChild(contentCard);
    });
  }

  createContentCard(item) {
    const card = document.createElement('div');
    card.className = 'content-card';
    card.dataset.contentId = item.id;
    card.dataset.contentType = item.type;

    const typeIcons = {
      article: '📰',
      video: '🎥',
      book: '📚'
    };

    card.innerHTML = `
      <div class="content-card-header">
        <span class="content-type-icon">${typeIcons[item.type]}</span>
        <span class="content-difficulty">Level ${item.difficultyLevel}</span>
      </div>
      <h4 class="content-title">${item.title}</h4>
      <p class="content-description">${item.description}</p>
      <div class="content-meta">
        <span class="content-duration">⏱️ ${item.estimatedDuration}min</span>
        <span class="content-source">📍 ${item.source}</span>
      </div>
      <div class="content-topics">
        ${item.topics.map(topic => `<span class="topic-tag">${topic}</span>`).join('')}
      </div>
      <button class="btn btn-primary start-content-btn">
        Start ${item.type === 'video' ? 'Watching' : 'Reading'} 
        <span class="points-preview">+${this.calculateContentPoints(item)} pts</span>
      </button>
    `;

    // Add click listener to start content
    card.querySelector('.start-content-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.startContent(item);
    });

    return card;
  }

  calculateContentPoints(item) {
    // Base points calculation
    const basePoints = 10;
    const difficultyMultiplier = item.difficultyLevel * 0.2;
    const durationBonus = Math.min(item.estimatedDuration / 5, 10);
    
    return Math.round(basePoints * (1 + difficultyMultiplier) + durationBonus);
  }

  async startContent(contentItem) {
    try {
      this.showLoading('Preparing your content...');
      
      const session = await this.contentManager.startSession(contentItem);
      if (session.success) {
        this.currentSession = session.session;
        await this.readingManager.startReading(session.session, session.chunkedContent);
        this.showScreen('reading');
      } else {
        this.showNotification(session.error || 'Failed to start content', 'error');
      }
    } catch (error) {
      console.error('Start content error:', error);
      this.showNotification('Failed to start content', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async loadActiveSessions() {
    try {
      const sessions = await this.contentManager.getActiveSessions();
      this.displayActiveSessions(sessions);
    } catch (error) {
      console.error('Failed to load active sessions:', error);
    }
  }

  displayActiveSessions(sessions) {
    const sessionsList = document.getElementById('active-sessions-list');
    sessionsList.innerHTML = '';

    if (!sessions || sessions.length === 0) {
      sessionsList.innerHTML = '<p class="no-sessions">No active sessions. Start reading something new!</p>';
      return;
    }

    sessions.forEach(session => {
      const sessionCard = this.createSessionCard(session);
      sessionsList.appendChild(sessionCard);
    });
  }

  createSessionCard(session) {
    const card = document.createElement('div');
    card.className = 'session-card';
    
    const typeIcons = {
      article: '📰',
      video: '🎥',
      book: '📚'
    };

    card.innerHTML = `
      <div class="session-icon">${typeIcons[session.contentType]}</div>
      <div class="session-info">
        <h4>${session.contentTitle}</h4>
        <div class="session-progress">
          <div class="progress-bar small">
            <div class="progress-fill" style="width: ${session.completionPercentage}%"></div>
          </div>
          <span>${session.completedChunks}/${session.totalChunks} chunks</span>
        </div>
      </div>
      <button class="btn btn-secondary continue-btn">Continue</button>
    `;

    card.querySelector('.continue-btn').addEventListener('click', () => {
      this.continueSession(session);
    });

    return card;
  }

  async continueSession(session) {
    try {
      this.showLoading('Loading your session...');
      this.currentSession = session;
      await this.readingManager.continueReading(session);
      this.showScreen('reading');
    } catch (error) {
      console.error('Continue session error:', error);
      this.showNotification('Failed to continue session', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async refreshContent() {
    try {
      this.showLoading('Getting fresh content...');
      const content = await this.contentManager.getCuratedContent(true);
      this.displayCuratedContent(content);
      this.showNotification('Content refreshed!', 'success');
    } catch (error) {
      console.error('Refresh content error:', error);
      this.showNotification('Failed to refresh content', 'error');
    } finally {
      this.hideLoading();
    }
  }

  handleLogout() {
    localStorage.removeItem('brainripe_token');
    this.currentUser = null;
    this.currentSession = null;
    this.showScreen('auth');
    this.showNotification('Logged out successfully', 'success');
  }

  showLoading(message = 'Loading...') {
    // You can implement a loading overlay here
    console.log('Loading:', message);
  }

  hideLoading() {
    // Hide loading overlay
    console.log('Loading complete');
  }

  showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };

    notification.innerHTML = `
      <span class="notification-icon">${icons[type]}</span>
      <span class="notification-message">${message}</span>
      <button class="notification-close">×</button>
    `;

    // Add close functionality
    notification.querySelector('.notification-close').addEventListener('click', () => {
      notification.remove();
    });

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }

  showContentType(contentType) {
    // Filter content list to show only selected type
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
      if (card.dataset.contentType === contentType) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });

    // Update category selection visual feedback
    document.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('selected');
    });
    document.querySelector(`[data-type="${contentType}"]`).classList.add('selected');
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('🌟 DOM loaded, initializing BrainRipe app...');

  // Set up manual continue button as fallback
  setTimeout(() => {
    const manualButton = document.getElementById('manual-continue');
    if (manualButton) {
      manualButton.style.display = 'block';
      manualButton.addEventListener('click', () => {
        if (window.brainRipeApp) {
          window.brainRipeApp.showScreen('auth');
        }
      });
    }
  }, 5000);

  // Set up debug button
  const debugButton = document.getElementById('debug-screens');
  if (debugButton) {
    debugButton.addEventListener('click', () => {
      console.log('🔍 Debug: All screens:');
      document.querySelectorAll('.screen').forEach(screen => {
        const isHidden = screen.classList.contains('hidden');
        const computedStyle = window.getComputedStyle(screen);
        const displayValue = computedStyle.display;
        console.log(`  - ${screen.id}: hidden=${isHidden}, display=${displayValue}`);
      });

      if (window.brainRipeApp) {
        console.log('🔄 Testing screen switch to auth...');
        window.brainRipeApp.showScreen('auth');
      }
    });
  }

  try {
    window.brainRipeApp = new BrainRipeApp();
    console.log('🎯 BrainRipe app instance created');
  } catch (error) {
    console.error('❌ Failed to create app instance:', error);
  }
});
