<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainRipe Parent Dashboard</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/parent.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="brain-logo">🧠🍎</div>
            <h1>BrainRipe Parent Dashboard</h1>
            <div class="loading-spinner"></div>
            <p>Loading your child's progress...</p>
        </div>
    </div>

    <!-- Parent Authentication -->
    <div id="parent-auth-screen" class="screen hidden">
        <div class="parent-auth-container">
            <div class="parent-auth-header">
                <div class="logo">
                    <span class="brain-icon">🧠</span>
                    <span class="apple-icon">🍎</span>
                    <h1>BrainRipe</h1>
                </div>
                <h2>Parent Dashboard</h2>
                <p>Monitor your child's learning progress</p>
            </div>

            <form id="parent-login-form">
                <div class="parent-form-group">
                    <label for="parent-email">Parent Email</label>
                    <input type="email" id="parent-email" required>
                </div>
                <div class="parent-form-group">
                    <label for="parent-password">Parent Password</label>
                    <input type="password" id="parent-password" required>
                </div>
                <button type="submit" class="parent-login-btn">Login to Dashboard</button>
            </form>
            <p class="auth-note">
                Don't have a parent account? Set one up from your child's account.
            </p>
        </div>
    </div>

    <!-- Parent Dashboard -->
    <div id="parent-dashboard-screen" class="screen hidden">
        <div class="parent-dashboard-container">
            <!-- Navigation Header -->
            <header class="parent-header">
                <div class="parent-header-left">
                    <div class="logo-small">🧠🍎</div>
                    <h1>Parent Dashboard</h1>
                </div>
                <div class="child-info">
                    <div class="child-avatar">👤</div>
                    <div class="child-details">
                        <h3 id="child-name">Loading...</h3>
                        <p>Level <span id="child-level">1</span></p>
                    </div>
                </div>
                <div class="parent-header-right">
                    <button id="settings-btn" class="btn btn-icon edit-btn">⚙️</button>
                    <button id="parent-logout-btn" class="btn btn-icon edit-btn">🚪</button>
                </div>
            </header>

            <!-- Main Dashboard Content -->
            <div class="parent-dashboard-grid">
                <!-- Progress Overview -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Weekly Progress</h3>
                        <div class="card-icon">📊</div>
                    </div>
                    <div id="weekly-progress" class="progress-item">
                        <div class="progress-label">This Week</div>
                        <div class="progress-value">0/100 points</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Progress -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Monthly Progress</h3>
                        <div class="card-icon">📈</div>
                    </div>
                    <div id="monthly-progress" class="progress-item">
                        <div class="progress-label">This Month</div>
                        <div class="progress-value">0/400 points</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Screen Time -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Screen Time</h3>
                        <div class="card-icon">⏱️</div>
                    </div>
                    <div id="screen-time" class="progress-item">
                        <div class="progress-label">Today</div>
                        <div class="progress-value">0/120 min</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Reading Streak -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Reading Streak</h3>
                        <div class="card-icon">🔥</div>
                    </div>
                    <div id="reading-streak" class="progress-item">
                        <div class="progress-label">Current Streak</div>
                        <div class="progress-value">0 days</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Goals and Settings -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Goals & Settings</h3>
                    <button id="edit-goals-btn" class="edit-btn">Edit Goals</button>
                </div>

                <div class="goal-item">
                    <span>Weekly Goal:</span>
                    <span id="weekly-goal-display">100 points</span>
                </div>
                <div class="goal-item">
                    <span>Monthly Goal:</span>
                    <span id="monthly-goal-display">400 points</span>
                </div>
                <div class="goal-item">
                    <span>Screen Time Limit:</span>
                    <span id="screen-time-limit-display">120 minutes</span>
                </div>
            </div>

            <!-- Content Restrictions -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Content Settings</h3>
                    <button id="edit-restrictions-btn" class="edit-btn">Edit Settings</button>
                </div>

                <div class="restriction-item">
                    <span>Allowed Content:</span>
                    <span id="allowed-content-types">article, video, book</span>
                </div>
                <div class="restriction-item">
                    <span>Blocked Topics:</span>
                    <span id="blocked-topics-display">None</span>
                </div>
                <div class="restriction-item">
                    <span>Social Features:</span>
                    <span id="social-features-display">Enabled</span>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-card activity-feed">
                <div class="card-header">
                    <h3>Recent Activity</h3>
                    <select id="activity-filter" class="filter-select">
                        <option value="all">All Activity</option>
                        <option value="completed">Completed Sessions</option>
                        <option value="achievements">Achievements</option>
                    </select>
                </div>

                <div id="activity-feed-container">
                    <!-- Activity items will be populated here -->
                    <div class="no-activity">
                        <p>Loading activity...</p>
                    </div>
                </div>
            </div>

            <!-- Progress Charts -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Progress Over Time</h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" data-period="week">Week</button>
                        <button class="chart-btn" data-period="month">Month</button>
                    </div>
                </div>

                <div class="chart-container">
                    <!-- Chart will be rendered here -->
                </div>
            </div>

            <!-- Stats Summary -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Learning Stats</h3>
                    <div class="card-icon">📊</div>
                </div>

                <div class="progress-item">
                    <span class="progress-label">Total Sessions:</span>
                    <span class="progress-value" id="total-sessions">0</span>
                </div>
                <div class="progress-item">
                    <span class="progress-label">Average Daily Time:</span>
                    <span class="progress-value" id="avg-daily-time">0 min</span>
                </div>
                <div class="progress-item">
                    <span class="progress-label">Favorite Content:</span>
                    <span class="progress-value" id="favorite-content-type">None</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Parent Settings</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <!-- Settings form will be populated here -->
            </div>
        </div>
    </div>

    <!-- Goals Edit Modal -->
    <div id="goals-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Goals</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <form id="goals-form">
                    <div class="form-group">
                        <label for="weekly-goal-input">Weekly Points Goal</label>
                        <input type="number" id="weekly-goal-input" min="50" max="1000" step="10">
                    </div>
                    <div class="form-group">
                        <label for="monthly-goal-input">Monthly Points Goal</label>
                        <input type="number" id="monthly-goal-input" min="200" max="5000" step="50">
                    </div>
                    <div class="form-group">
                        <label for="screen-time-input">Daily Screen Time Limit (minutes)</label>
                        <input type="number" id="screen-time-input" min="30" max="300" step="15">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancel-goals">Cancel</button>
                <button class="btn btn-primary" id="save-goals">Save Goals</button>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notification-container" class="notification-container">
        <!-- Notifications will appear here -->
    </div>

    <!-- Scripts -->
    <script src="scripts/parent-auth.js"></script>
    <script src="scripts/parent-dashboard.js"></script>
    <script src="scripts/parent-app.js"></script>
</body>
</html>
