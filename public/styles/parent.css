/* Parent Dashboard Specific Styles */

/* Parent Authentication Screen */
.parent-auth-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.parent-auth-header {
  margin-bottom: 2rem;
}

.parent-auth-header h2 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-family: 'Fredoka One', cursive;
}

.parent-auth-header p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.parent-form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.parent-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.parent-form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e6ed;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.parent-form-group input:focus {
  outline: none;
  border-color: #3498db;
}

.parent-login-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.parent-login-btn:hover {
  transform: translateY(-2px);
}

/* Parent Dashboard Layout */
.parent-dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.parent-header {
  background: white;
  border-radius: 15px;
  padding: 1rem 2rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.parent-header-left h1 {
  color: #2c3e50;
  margin: 0;
  font-family: 'Fredoka One', cursive;
}

.child-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.child-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.child-details h3 {
  margin: 0;
  color: #2c3e50;
}

.child-details p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.parent-header-right {
  display: flex;
  gap: 1rem;
}

/* Dashboard Grid */
.parent-dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboard-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
  font-family: 'Fredoka One', cursive;
}

.card-icon {
  font-size: 1.5rem;
}

/* Progress Cards */
.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #ecf0f1;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-label {
  font-weight: 600;
  color: #2c3e50;
}

.progress-value {
  color: #27ae60;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  transition: width 0.3s ease;
}

/* Goals and Restrictions */
.goal-item, .restriction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.edit-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.edit-btn:hover {
  background: #2980b9;
}

/* Activity Feed */
.activity-feed {
  grid-column: 1 / -1;
}

.activity-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-select {
  padding: 0.5rem;
  border: 2px solid #e0e6ed;
  border-radius: 8px;
  background: white;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  margin-bottom: 0.5rem;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.activity-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.activity-time {
  color: #95a5a6;
  font-size: 0.8rem;
}

/* Charts */
.chart-container {
  position: relative;
  height: 300px;
  margin-top: 1rem;
}

.chart-controls {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.chart-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e0e6ed;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal.hidden {
  display: none;
}

.modal-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .parent-dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .parent-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .child-info {
    justify-content: center;
  }
}
