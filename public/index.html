<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BrainRipe - Learn, Play, Grow! 🧠🍎</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="styles/reading.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="screen loading-screen">
        <div class="loading-content">
            <div class="brain-logo">🧠🍎</div>
            <h1>BrainRipe</h1>
            <div class="loading-spinner"></div>
            <p>Loading your learning adventure...</p>
            <button id="manual-continue" style="margin-top: 20px; padding: 10px 20px; background: #4299e1; color: white; border: none; border-radius: 5px; cursor: pointer; display: none;">Continue Manually</button>
            <button id="debug-screens" style="margin-top: 10px; padding: 10px 20px; background: #e53e3e; color: white; border: none; border-radius: 5px; cursor: pointer;">Debug Screens</button>
        </div>
    </div>

    <!-- Authentication Screen -->
    <div id="auth-screen" class="screen hidden">
        <div class="auth-container">
            <div class="auth-header">
                <div class="logo">
                    <span class="brain-icon">🧠</span>
                    <span class="apple-icon">🍎</span>
                    <h1>BrainRipe</h1>
                </div>
                <p class="tagline">Learn, Play, Grow!</p>
            </div>

            <!-- Login Form -->
            <div id="login-form" class="auth-form">
                <h2>Welcome Back!</h2>
                <form id="login-form-element">
                    <div class="form-group">
                        <label for="login-username">Username</label>
                        <input type="text" id="login-username" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
                <p class="auth-switch">
                    New to BrainRipe? <a href="#" id="show-register">Create Account</a>
                </p>
            </div>

            <!-- Registration Form -->
            <div id="register-form" class="auth-form hidden">
                <h2>Join BrainRipe!</h2>
                <form id="register-form-element">
                    <div class="form-group">
                        <label for="register-username">Choose a Username</label>
                        <input type="text" id="register-username" required>
                    </div>
                    <div class="form-group">
                        <label for="register-password">Create Password</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <div class="form-group">
                        <label for="register-birthdate">Your Birthday</label>
                        <input type="date" id="register-birthdate" required>
                    </div>
                    <div class="form-group">
                        <label>What interests you? (Optional)</label>
                        <div class="interests-grid">
                            <label class="interest-item">
                                <input type="checkbox" value="science"> Science 🔬
                            </label>
                            <label class="interest-item">
                                <input type="checkbox" value="animals"> Animals 🐾
                            </label>
                            <label class="interest-item">
                                <input type="checkbox" value="space"> Space 🚀
                            </label>
                            <label class="interest-item">
                                <input type="checkbox" value="sports"> Sports ⚽
                            </label>
                            <label class="interest-item">
                                <input type="checkbox" value="art"> Art 🎨
                            </label>
                            <label class="interest-item">
                                <input type="checkbox" value="music"> Music 🎵
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Create Account</button>
                </form>
                <p class="auth-switch">
                    Already have an account? <a href="#" id="show-login">Login</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard-screen" class="screen hidden">
        <!-- Navigation Header -->
        <header class="main-header">
            <div class="header-left">
                <div class="logo-small">🧠🍎</div>
                <h1>BrainRipe</h1>
            </div>
            <div class="header-center">
                <div class="user-stats">
                    <div class="stat-item">
                        <span class="stat-icon">⭐</span>
                        <span id="user-points">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🏆</span>
                        <span id="user-level">1</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🎯</span>
                        <span id="weekly-progress">0/100</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <button id="profile-btn" class="btn btn-icon">👤</button>
                <button id="logout-btn" class="btn btn-icon">🚪</button>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="dashboard-main">
            <!-- Content Hub -->
            <section id="content-hub" class="hub-container">
                <div class="hub-header">
                    <h2>Choose Your Adventure!</h2>
                    <button id="refresh-content" class="btn btn-secondary">
                        <span>🔄</span> Get New Content
                    </button>
                </div>

                <!-- Content Categories -->
                <div class="content-categories">
                    <div class="category-card" data-type="article">
                        <div class="category-icon">📰</div>
                        <h3>News & Articles</h3>
                        <p>Learn about the world around you</p>
                        <div class="category-count" id="article-count">0 available</div>
                    </div>
                    
                    <div class="category-card" data-type="video">
                        <div class="category-icon">🎥</div>
                        <h3>Educational Videos</h3>
                        <p>Watch and learn something new</p>
                        <div class="category-count" id="video-count">0 available</div>
                    </div>
                    
                    <div class="category-card" data-type="book">
                        <div class="category-icon">📚</div>
                        <h3>Stories & Books</h3>
                        <p>Dive into amazing stories</p>
                        <div class="category-count" id="book-count">0 available</div>
                    </div>
                </div>

                <!-- Content List -->
                <div id="content-list" class="content-grid">
                    <!-- Content items will be populated here -->
                </div>
            </section>

            <!-- Active Sessions -->
            <section id="active-sessions" class="section">
                <h3>Continue Reading</h3>
                <div id="active-sessions-list" class="sessions-list">
                    <!-- Active sessions will be populated here -->
                </div>
            </section>

            <!-- Quick Stats -->
            <section id="quick-stats" class="section">
                <h3>Your Progress</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📖</div>
                        <div class="stat-value" id="total-sessions">0</div>
                        <div class="stat-label">Sessions Completed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-value" id="total-time">0m</div>
                        <div class="stat-label">Time Spent Learning</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏅</div>
                        <div class="stat-value" id="total-badges">0</div>
                        <div class="stat-label">Badges Earned</div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Reading Interface -->
    <div id="reading-screen" class="screen hidden">
        <div class="reading-container">
            <!-- Reading Header -->
            <header class="reading-header">
                <button id="back-to-dashboard" class="btn btn-icon">← Back</button>
                <div class="reading-title">
                    <h2 id="content-title">Loading...</h2>
                    <p id="content-source">Source</p>
                </div>
                <div class="reading-controls">
                    <button id="pause-session" class="btn btn-secondary">⏸️ Pause</button>
                </div>
            </header>

            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <div class="progress-text">
                    <span id="current-chunk">1</span> / <span id="total-chunks">1</span>
                </div>
            </div>

            <!-- Chunk Content -->
            <div id="chunk-container" class="chunk-container">
                <div id="chunk-content" class="chunk-content">
                    <!-- Chunk content will be loaded here -->
                </div>
                
                <div class="chunk-controls">
                    <button id="complete-chunk" class="btn btn-primary btn-large">
                        I'm Done Reading! <span id="chunk-points">+10 points</span>
                    </button>
                </div>
            </div>

            <!-- Completion Celebration -->
            <div id="completion-modal" class="modal hidden">
                <div class="modal-content celebration">
                    <div class="celebration-icon">🎉</div>
                    <h3>Amazing Work!</h3>
                    <p>You've completed this content!</p>
                    <div class="completion-stats">
                        <div class="completion-stat">
                            <span class="stat-icon">⭐</span>
                            <span id="session-points">0</span> points earned
                        </div>
                        <div class="completion-stat">
                            <span class="stat-icon">⏱️</span>
                            <span id="session-time">0</span> minutes spent
                        </div>
                    </div>
                    <div class="completion-actions">
                        <button id="rate-content" class="btn btn-secondary">Rate This Content</button>
                        <button id="back-to-hub" class="btn btn-primary">Find More Content</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div id="notification-container" class="notification-container">
        <!-- Notifications will appear here -->
    </div>

    <!-- Points Animation Container -->
    <div id="points-animation-container" class="points-animation-container">
        <!-- Point animations will appear here -->
    </div>

    <!-- Scripts -->
    <script src="scripts/auth.js"></script>
    <script src="scripts/dashboard.js"></script>
    <script src="scripts/reading.js"></script>
    <script src="scripts/ai-content.js"></script>
    <script src="scripts/app.js"></script>
</body>
</html>
