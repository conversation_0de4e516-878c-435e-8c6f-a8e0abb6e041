<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screen Test</title>
    <style>
        .screen {
            min-height: 100vh;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-family: Arial, sans-serif;
        }
        
        .screen.hidden {
            display: none;
        }
        
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .auth-screen {
            background: #f0f0f0;
            color: #333;
        }
        
        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="showScreen('loading')">Show Loading</button>
        <button onclick="showScreen('auth')">Show Auth</button>
        <button onclick="debugScreens()">Debug Screens</button>
    </div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="screen loading-screen">
        <div>
            <h1>🧠🍎 Loading Screen</h1>
            <p>This is the loading screen</p>
        </div>
    </div>

    <!-- Auth Screen -->
    <div id="auth-screen" class="screen auth-screen hidden">
        <div>
            <h1>🔐 Auth Screen</h1>
            <p>This is the authentication screen</p>
        </div>
    </div>

    <script>
        function showScreen(screenName) {
            console.log(`🖥️ Showing screen: ${screenName}`);

            // Hide all screens first
            const allScreens = document.querySelectorAll('.screen');
            console.log(`📱 Found ${allScreens.length} screens to hide`);
            allScreens.forEach(screen => {
                screen.classList.add('hidden');
                console.log(`🙈 Hidden screen: ${screen.id}`);
            });

            // Show the requested screen
            const targetScreen = document.getElementById(`${screenName}-screen`);
            if (targetScreen) {
                targetScreen.classList.remove('hidden');
                console.log(`✅ ${screenName} screen displayed (${targetScreen.id})`);
                
                // Verify the screen is actually visible
                const isHidden = targetScreen.classList.contains('hidden');
                console.log(`🔍 Screen ${screenName} hidden class: ${isHidden}`);
            } else {
                console.error(`❌ Screen element not found: ${screenName}-screen`);
            }
        }

        function debugScreens() {
            console.log('🔍 Debug: All screens:');
            document.querySelectorAll('.screen').forEach(screen => {
                const isHidden = screen.classList.contains('hidden');
                console.log(`  - ${screen.id}: hidden=${isHidden}`);
            });
        }

        // Test automatic switching
        setTimeout(() => {
            console.log('🔄 Auto-switching to auth screen');
            showScreen('auth');
        }, 3000);
    </script>
</body>
</html>
